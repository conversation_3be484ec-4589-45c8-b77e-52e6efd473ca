/**
 * Qax Codegen 构建配置文件
 * 集中管理 Qax Codegen 扩展的所有配置信息
 * <AUTHOR>
 */

const path = require("path")
const fs = require("fs")

// 获取项目根目录
const ROOT_DIR = path.resolve(__dirname, "../..")
const QAX_DIR = path.resolve(__dirname, "..")

// 环境配置
const ENVIRONMENTS = {
	dev: {
		name: "development",
		qaxDomain: "https://codegen-dev.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
		// 开发环境：不压缩，保留 sourcemap，启用调试
		esbuildArgs: "", // 不使用 --production
		minify: false,
		sourcemap: true,
		debug: true,
	},
	test: {
		name: "testing",
		qaxDomain: "https://codegen-test.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
		// 测试环境：轻度优化，保留 sourcemap，便于调试
		esbuildArgs: "", // 不使用 --production
		minify: false,
		sourcemap: true,
		debug: false,
	},
	prod: {
		name: "production",
		qaxDomain: "https://codegen.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
		// 生产环境：完全优化，压缩代码，移除 sourcemap
		esbuildArgs: "--production",
		minify: true,
		sourcemap: false,
		debug: false,
	},
}

// 获取当前环境配置
function getEnvironmentConfig(env = "prod") {
	return ENVIRONMENTS[env] || ENVIRONMENTS.prod
}

// 文本替换规则
const TEXT_REPLACEMENTS = {
	// 扩展标识符替换
	'"saoudrizwan.claude-dev"': '"qi-anxin-group.qax-codegen"',
	"'saoudrizwan.claude-dev'": "'qi-anxin-group.qax-codegen'",
	"saoudrizwan.claude-dev": "qi-anxin-group.qax-codegen",

	// 命令名称替换（精确匹配，避免误替换）
	'registerCommand("cline.': 'registerCommand("qax-codegen.',
	"registerCommand('cline.": "registerCommand('qax-codegen.",
	'executeCommand("cline.': 'executeCommand("qax-codegen.',
	"executeCommand('cline.": "executeCommand('qax-codegen.",
	'command: "cline.': 'command: "qax-codegen.',
	"command: 'cline.": "command: 'qax-codegen.",
	'"setContext", "cline.': '"setContext", "qax-codegen.',
	"'setContext', 'cline.": "'setContext', 'qax-codegen.",

	// 视图和容器ID替换（在限定文件范围内相对安全）
	'"claude-dev.': '"qax-codegen.',
	"'claude-dev.": "'qax-codegen.",
	"claude-dev.": "qax-codegen.",

	// ActivityBar 容器ID替换
	"claude-dev-ActivityBar": "qax-codegen-ActivityBar",
	'"claude-dev-ActivityBar"': '"qax-codegen-ActivityBar"',
	"'claude-dev-ActivityBar'": "'qax-codegen-ActivityBar'",

	// Walkthrough ID 替换
	"saoudrizwan.claude-dev#ClineWalkthrough": "qi-anxin-group.qax-codegen#QaxCodegenWalkthrough",
	'"saoudrizwan.claude-dev#ClineWalkthrough"': '"qi-anxin-group.qax-codegen#QaxCodegenWalkthrough"',
	"'saoudrizwan.claude-dev#ClineWalkthrough'": "'qi-anxin-group.qax-codegen#QaxCodegenWalkthrough'",

	// Output Channel 和 Terminal 名称替换
	'createOutputChannel("Cline")': 'createOutputChannel("Qax Codegen")',
	"createOutputChannel('Cline')": "createOutputChannel('Qax Codegen')",
	'createOutputChannel("Cline Commit Generator")': 'createOutputChannel("Qax Codegen Commit Generator")',
	"createOutputChannel('Cline Commit Generator')": "createOutputChannel('Qax Codegen Commit Generator')",
	'name: "Cline"': 'name: "Qax Codegen"',
	"name: 'Cline'": "name: 'Qax Codegen'",

	// 特定的日志和消息
	"Cline extension activated": "Qax Codegen extension activated",
	"Cline extension deactivated": "Qax Codegen extension deactivated",
	"ClineProvider instantiated": "Qax Codegen Provider instantiated",
	"Cline wants to": "Qax Codegen wants to",
	"Cline has a": "Qax Codegen has a",
	"Cline is suggesting": "Qax Codegen is suggesting",
}

// 需要临时复制的文件列表
const TEMP_COPY_FILES = [
	{
		from: "qax/assets/icons/qax-icon.png",
		to: "assets/icons/qax-icon.png",
	},
	{
		from: "qax/assets/icons/qax-sidebar-icon.png",
		to: "assets/icons/qax-sidebar-icon.png",
	},
	{
		from: "qax/docs/README.md",
		to: "README.md",
	},
	{
		from: "qax/docs/CHANGELOG.md",
		to: "CHANGELOG.md",
	},
	{
		from: "qax/webview-ui/index.html",
		to: "webview-ui/index.html",
	},
]

// 需要应用文本替换的文件模式（这些文件会被临时修改，构建后自动恢复）
const TEXT_REPLACEMENT_PATTERNS = {
	// 核心扩展文件 - 打包必需
	//- 包含命令注册的文件
	"src/extension.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	//- 包含视图ID的文件
	"src/core/webview/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	//- 包含命令调用的文件
	"src/core/controller/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含扩展引用的文件
	"src/core/task/ToolExecutor.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含扩展标识符的文件
	"src/standalone/vscode-context.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含终端名称的文件
	"src/integrations/terminal/TerminalRegistry.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// 其他可能， 非必需
	//- 开发和工具文件（可选，用于开发测试）
	// "src/dev/commands/tasks.ts": {
	// 	textReplacements: TEXT_REPLACEMENTS,
	// },
	//- 评估系统文件（可选，用于开发测试）
	// "evals/cli/src/utils/vscode.ts": {
	// 	textReplacements: TEXT_REPLACEMENTS,
	// },
}

// 环境变量配置
function getEnvironmentVariables(env = "prod") {
	const envConfig = getEnvironmentConfig(env)

	return {
		// QAX 基础配置
		VSCODE_QAX_MODE: "true",
		VSCODE_QAX_ENV: env,
		VSCODE_QAX_CODEGEN_DOMAIN: envConfig.qaxDomain,
		VSCODE_QAX_AI_DOMAIN: envConfig.aiDomain,
		VSCODE_QAX_AUTOCOMPLETE: "true",

		// 扩展标识符
		VSCODE_EXTENSION_ID: "qi-anxin-group.qax-codegen",

		// 构建相关环境变量
		NODE_ENV: envConfig.name,
		IS_DEV: envConfig.debug.toString(),
		ENABLE_SOURCEMAP: envConfig.sourcemap.toString(),
		ENABLE_MINIFY: envConfig.minify.toString(),

		// 调试和日志级别
		DEBUG: envConfig.debug ? "*" : "",
		LOG_LEVEL: envConfig.debug ? "debug" : "info",
	}
}

/**
 * 根据环境生成构建脚本配置
 * @param {string} env - 环境名称 (dev/test/prod)
 * @returns {Object} 构建脚本配置
 */
function getBuildScripts(env = "prod") {
	return {
		// 预构建脚本 - 执行准备工作
		prebuild: [
			// 仅生成 proto 文件，类型检查留给 package 命令
			"npm run protos",
		],

		// 构建脚本 - 使用 Cline 原生的 package 命令
		// 优化思路：
		// 1. 直接使用 npm run package，避免重复构建
		// 2. package 命令包含：check-types + build:webview + lint + esbuild --production
		// 3. 这样 vsce package 时不会重复执行构建步骤
		build: [
			// 使用 Cline 原生的完整构建命令
			env === "dev"
				? // 开发环境：分步执行以便调试
					"npm run check-types && npm run dev:webview && npm run lint && node esbuild.js"
				: // 生产环境：使用原生 package 命令
					"npm run package",
		],

		// 后构建脚本
		postbuild: [
			// 可以添加后处理脚本，如构建产物验证、压缩优化等
		],
	}
}

// 为了向后兼容，保留默认的 BUILD_SCRIPTS（生产环境）
const BUILD_SCRIPTS = getBuildScripts("prod")

// 构建优化配置
const BUILD_CONFIG = {
	// 构建超时时间（毫秒）
	buildTimeout: 300000, // 5分钟
	// 是否显示详细的构建时间统计
	showDetailedTiming: true,
	// 备份保留数量
	maxBackupCount: 3,
	// 构建步骤说明
	buildSteps: {
		prebuild: "准备工作：生成 proto 文件",
		webview: "前端构建：TypeScript 增量编译 + Vite 打包",
		lint: "代码检查：ESLint + Buf lint",
		esbuild: "主构建：ESBuild 打包扩展",
		package: "完整构建：类型检查 + 前端构建 + 代码检查 + 主构建",
	},
}

/**
 * 合并 package.json 配置
 * @param {string} originalPackageJsonPath - 原始 package.json 路径
 * @param {string} qaxPackageJsonPath - Qax package.json 路径
 * @returns {Object} 合并后的 package.json 对象
 */
function mergePackageJson(originalPackageJsonPath, qaxPackageJsonPath) {
	// 读取原始 package.json
	const originalPackage = JSON.parse(fs.readFileSync(originalPackageJsonPath, "utf8"))

	// 读取 Qax package.json
	const qaxPackage = JSON.parse(fs.readFileSync(qaxPackageJsonPath, "utf8"))

	// 深度合并对象
	const mergedPackage = deepMerge(originalPackage, qaxPackage)

	console.log(`✅ 已合并 package.json 配置`)
	console.log(`   - 原始文件: ${path.relative(ROOT_DIR, originalPackageJsonPath)}`)
	console.log(`   - Qax 配置: ${path.relative(ROOT_DIR, qaxPackageJsonPath)}`)
	console.log(`   - 扩展标识符: ${mergedPackage.name}`)
	console.log(`   - 显示名称: ${mergedPackage.displayName}`)
	console.log(`   - 版本号: ${mergedPackage.version}`)

	return mergedPackage
}

/**
 * 覆盖合并对象 - Qax 配置完全覆盖原始配置
 * @param {Object} target - 目标对象
 * @param {Object} source - 源对象（Qax 配置）
 * @returns {Object} 合并后的对象
 */
function deepMerge(target, source) {
	const result = { ...target }

	for (const key in source) {
		if (source.hasOwnProperty(key)) {
			// Qax 配置直接覆盖原始配置，不进行深度合并
			result[key] = source[key]
		}
	}

	return result
}

// 路径配置
const PATHS = {
	root: ROOT_DIR,
	qax: QAX_DIR,
	originalPackageJson: path.join(ROOT_DIR, "package.json"),
	qaxPackageJson: path.join(QAX_DIR, "package.json"),
	dist: path.join(ROOT_DIR, "dist"),
	webviewBuild: path.join(ROOT_DIR, "webview-ui/build"),
	clineBackup: path.join(QAX_DIR, "cline-backup"),
}

// 导出配置
module.exports = {
	ROOT_DIR,
	QAX_DIR,
	ENVIRONMENTS,
	TEXT_REPLACEMENTS,
	TEMP_COPY_FILES,
	TEXT_REPLACEMENT_PATTERNS,
	BUILD_SCRIPTS,
	BUILD_CONFIG,
	PATHS,
	getEnvironmentConfig,
	getEnvironmentVariables,
	getBuildScripts,
	mergePackageJson,
	deepMerge,
}
