"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QaxJumpSuggestionEngine = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const QaxNextEditTypes_1 = require("../types/QaxNextEditTypes");
const QaxLSPService_1 = require("./QaxLSPService");
/**
 * 跳转和建议引擎，提供精确的跳转位置和修改建议
 */
class QaxJumpSuggestionEngine {
    constructor(config) {
        this.config = config;
        this.lspService = QaxLSPService_1.QaxLSPService.getInstance();
    }
    /**
     * 生成跳转建议
     */
    async generateJumpSuggestions(changes, context) {
        const suggestions = [];
        for (const change of changes) {
            try {
                switch (change.type) {
                    case QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME:
                        const renameSuggestions = await this.generateVariableRenameSuggestions(change, context);
                        suggestions.push(...renameSuggestions);
                        break;
                    case QaxNextEditTypes_1.QaxChangeType.FUNCTION_PARAMETER_CHANGE:
                        const paramSuggestions = await this.generateParameterChangeSuggestions(change, context);
                        suggestions.push(...paramSuggestions);
                        break;
                    case QaxNextEditTypes_1.QaxChangeType.FUNCTION_CALL_DELETION:
                        const deletionSuggestions = await this.generateCallDeletionSuggestions(change, context);
                        suggestions.push(...deletionSuggestions);
                        break;
                    case QaxNextEditTypes_1.QaxChangeType.VARIABLE_DELETION:
                        const varDeletionSuggestions = await this.generateVariableDeletionSuggestions(change, context);
                        suggestions.push(...varDeletionSuggestions);
                        break;
                    case QaxNextEditTypes_1.QaxChangeType.IMPORT_CHANGE:
                        const importSuggestions = await this.generateImportChangeSuggestions(change, context);
                        suggestions.push(...importSuggestions);
                        break;
                    case QaxNextEditTypes_1.QaxChangeType.TYPE_CHANGE:
                        const typeSuggestions = await this.generateTypeChangeSuggestions(change, context);
                        suggestions.push(...typeSuggestions);
                        break;
                }
            }
            catch (error) {
                console.warn(`QaxJumpSuggestionEngine: Failed to generate suggestions for ${change.type}:`, error);
            }
        }
        // 排序和限制数量
        const sortedSuggestions = suggestions
            .sort((a, b) => b.priority - a.priority)
            .slice(0, this.config.maxSuggestions);
        return sortedSuggestions;
    }
    /**
     * 生成变量重命名建议
     */
    async generateVariableRenameSuggestions(change, context) {
        const suggestions = [];
        if (!change.newValue || !change.metadata?.symbolName) {
            return suggestions;
        }
        // 获取所有引用位置
        const references = change.metadata.references || [];
        // 如果没有 LSP 引用信息，尝试通过 LSP 获取
        if (references.length === 0) {
            try {
                const position = change.range.start;
                const lspReferences = await this.lspService.getReferences(context.document, position);
                references.push(...lspReferences);
            }
            catch (error) {
                console.warn("Failed to get LSP references:", error);
            }
        }
        // 为每个引用位置生成建议
        for (const reference of references) {
            // 跳过当前修改位置
            if (reference.uri.fsPath === context.filePath &&
                reference.range.isEqual(change.range)) {
                continue;
            }
            const suggestion = {
                id: `rename-${change.metadata.symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
                filePath: reference.uri.fsPath,
                range: reference.range,
                description: `Update variable name from '${change.oldValue}' to '${change.newValue}'`,
                changeType: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
                suggestedEdit: {
                    range: reference.range,
                    newText: change.newValue,
                    description: `Rename '${change.oldValue}' to '${change.newValue}'`
                },
                priority: this.calculatePriority(reference, context, change),
                relatedChange: change
            };
            suggestions.push(suggestion);
        }
        return suggestions;
    }
    /**
     * 生成函数参数变更建议
     */
    async generateParameterChangeSuggestions(change, context) {
        const suggestions = [];
        if (!change.metadata?.symbolName) {
            return suggestions;
        }
        const functionName = change.metadata.symbolName;
        // 查找函数调用位置
        const callSites = await this.findFunctionCallSites(functionName, context);
        for (const callSite of callSites) {
            const suggestion = {
                id: `param-change-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
                filePath: callSite.uri.fsPath,
                range: callSite.range,
                description: `Update function call '${functionName}' to match new parameters`,
                changeType: QaxNextEditTypes_1.QaxChangeType.FUNCTION_PARAMETER_CHANGE,
                suggestedEdit: {
                    range: callSite.range,
                    newText: this.generateUpdatedFunctionCall(callSite, change),
                    description: `Update parameters for '${functionName}'`
                },
                priority: this.calculatePriority(callSite, context, change),
                relatedChange: change
            };
            suggestions.push(suggestion);
        }
        return suggestions;
    }
    /**
     * 生成函数调用删除建议
     */
    async generateCallDeletionSuggestions(change, context) {
        const suggestions = [];
        if (!change.metadata?.symbolName) {
            return suggestions;
        }
        const functionName = change.metadata.symbolName;
        // 查找其他相关的函数调用
        const relatedCalls = await this.findFunctionCallSites(functionName, context);
        for (const callSite of relatedCalls) {
            // 跳过已删除的调用
            if (callSite.uri.fsPath === context.filePath &&
                callSite.range.intersection(change.range)) {
                continue;
            }
            const suggestion = {
                id: `call-deletion-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
                filePath: callSite.uri.fsPath,
                range: callSite.range,
                description: `Consider removing this call to '${functionName}' as well`,
                changeType: QaxNextEditTypes_1.QaxChangeType.FUNCTION_CALL_DELETION,
                suggestedEdit: {
                    range: callSite.range,
                    newText: "",
                    description: `Remove call to '${functionName}'`
                },
                priority: this.calculatePriority(callSite, context, change) - 2, // 降低优先级
                relatedChange: change
            };
            suggestions.push(suggestion);
        }
        return suggestions;
    }
    /**
     * 生成变量删除建议
     */
    async generateVariableDeletionSuggestions(change, context) {
        const suggestions = [];
        if (!change.metadata?.symbolName) {
            return suggestions;
        }
        // 查找变量的所有使用位置
        const references = change.metadata.references || [];
        for (const reference of references) {
            // 跳过删除位置
            if (reference.uri.fsPath === context.filePath &&
                reference.range.intersection(change.range)) {
                continue;
            }
            const suggestion = {
                id: `var-deletion-${change.metadata.symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
                filePath: reference.uri.fsPath,
                range: reference.range,
                description: `Remove usage of deleted variable '${change.metadata.symbolName}'`,
                changeType: QaxNextEditTypes_1.QaxChangeType.VARIABLE_DELETION,
                suggestedEdit: {
                    range: reference.range,
                    newText: "",
                    description: `Remove reference to '${change.metadata.symbolName}'`
                },
                priority: this.calculatePriority(reference, context, change),
                relatedChange: change
            };
            suggestions.push(suggestion);
        }
        return suggestions;
    }
    /**
     * 生成导入变更建议
     */
    async generateImportChangeSuggestions(change, context) {
        // 暂时返回空数组，可以后续实现
        return [];
    }
    /**
     * 生成类型变更建议
     */
    async generateTypeChangeSuggestions(change, context) {
        // 暂时返回空数组，可以后续实现
        return [];
    }
    /**
     * 查找函数调用位置
     */
    async findFunctionCallSites(functionName, context) {
        const callSites = [];
        try {
            // 简化实现：只在当前文档中查找函数调用
            const document = context.document;
            const content = document.getText();
            const lines = content.split('\n');
            const callPattern = new RegExp(`\\b${functionName}\\s*\\(`, 'g');
            for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
                const line = lines[lineIndex];
                let match;
                while ((match = callPattern.exec(line)) !== null) {
                    const startPos = new vscode.Position(lineIndex, match.index);
                    const endPos = new vscode.Position(lineIndex, match.index + match[0].length);
                    const range = new vscode.Range(startPos, endPos);
                    const location = new vscode.Location(document.uri, range);
                    callSites.push(location);
                }
            }
        }
        catch (error) {
            console.warn("Failed to find function call sites:", error);
        }
        return callSites;
    }
    /**
     * 生成更新后的函数调用
     */
    generateUpdatedFunctionCall(callSite, change) {
        // 这里需要根据具体的参数变更生成新的函数调用
        // 暂时返回原始文本，实际实现需要解析函数调用并更新参数
        return "/* TODO: Update function call parameters */";
    }
    /**
     * 计算建议的优先级
     */
    calculatePriority(location, context, change) {
        let priority = 5; // 基础优先级
        // 同一文件的建议优先级更高
        if (location.uri.fsPath === context.filePath) {
            priority += 3;
        }
        // 距离变更位置越近，优先级越高
        if (location.uri.fsPath === context.filePath) {
            const lineDiff = Math.abs(location.range.start.line - change.range.start.line);
            if (lineDiff <= 10) {
                priority += 2;
            }
            else if (lineDiff <= 50) {
                priority += 1;
            }
        }
        // 根据置信度调整优先级
        priority += Math.floor(change.confidence * 3);
        // 根据文件类型调整优先级
        const ext = path.extname(location.uri.fsPath).toLowerCase();
        if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
            priority += 1; // TypeScript/JavaScript 文件优先级稍高
        }
        return Math.max(1, Math.min(10, priority)); // 限制在 1-10 范围内
    }
    /**
     * 更新配置
     */
    updateConfig(config) {
        this.config = config;
    }
}
exports.QaxJumpSuggestionEngine = QaxJumpSuggestionEngine;
