"use strict";
/**
 * Integration test for QaxNextEdit with NextEdit service
 * Tests the complete workflow from QaxNextEdit suggestions to NextEdit format
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
// Mock VS Code API
const mockVscode = {
    workspace: {
        onDidChangeTextDocument: () => ({ dispose: () => { } }),
        onDidOpenTextDocument: () => ({ dispose: () => { } }),
        onDidCloseTextDocument: () => ({ dispose: () => { } }),
        onDidChangeConfiguration: () => ({ dispose: () => { } }),
        getConfiguration: (section) => ({
            get: (key, defaultValue) => {
                if (section === "qax-code.nextEdit" && key === "useQaxNextEdit") {
                    return true; // Enable QaxNextEdit for testing
                }
                return defaultValue;
            },
            update: () => Promise.resolve()
        }),
        openTextDocument: async (options) => ({
            uri: { fsPath: options.content ? "test.ts" : "test.ts" },
            languageId: options.language || "typescript",
            getText: (range) => {
                const content = options.content || "let oldName = 5;\nconsole.log(oldName);";
                if (!range)
                    return content;
                const lines = content.split('\n');
                return lines[range.start.line]?.substring(range.start.character, range.end.character) || "";
            },
            offsetAt: (position) => position.line * 100 + position.character
        }),
        textDocuments: []
    },
    window: {
        onDidChangeActiveTextEditor: () => ({ dispose: () => { } }),
        createStatusBarItem: () => ({
            text: "",
            show: () => { },
            hide: () => { },
            dispose: () => { }
        }),
        createTextEditorDecorationType: () => ({
            dispose: () => { }
        }),
        showTextDocument: async (document) => ({
            document,
            setDecorations: () => { },
            selection: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
            revealRange: () => { }
        }),
        activeTextEditor: null
    },
    languages: {
        onDidChangeDiagnostics: () => ({ dispose: () => { } }),
        registerHoverProvider: () => ({ dispose: () => { } })
    },
    commands: {
        executeCommand: async () => null,
        registerCommand: () => ({ dispose: () => { } })
    },
    StatusBarAlignment: { Right: 2 },
    TextEditorRevealType: { InCenter: 1 },
    SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
    ConfigurationTarget: { Global: 1 },
    ThemeColor: class {
        constructor(id) {
            this.id = id;
        }
    },
    MarkdownString: class {
        constructor(value = "") {
            this.value = value;
            this.isTrusted = false;
            this.isTrusted = false;
        }
        appendMarkdown(value) { this.value += value; return this; }
        appendCodeblock(value, language) {
            this.value += `\n\`\`\`${language || ''}\n${value}\n\`\`\`\n`;
            return this;
        }
    },
    Hover: class {
        constructor(contents, range) {
            this.contents = contents;
            this.range = range;
        }
    },
    Uri: {
        file: (path) => ({ fsPath: path, scheme: "file" })
    },
    Range: class {
        constructor(start, end) {
            this.start = start;
            this.end = end;
        }
        get isEmpty() {
            return this.start.line === this.end.line && this.start.character === this.end.character;
        }
        isEqual(other) {
            return this.start.line === other.start.line &&
                this.start.character === other.start.character &&
                this.end.line === other.end.line &&
                this.end.character === other.end.character;
        }
    },
    Position: class {
        constructor(line, character) {
            this.line = line;
            this.character = character;
        }
        isEqual(other) {
            return this.line === other.line && this.character === other.character;
        }
    },
    Selection: class {
        constructor(start, end) {
            this.start = start;
            this.end = end;
        }
    },
    Location: class {
        constructor(uri, range) {
            this.uri = uri;
            this.range = range;
        }
    }
};
// Apply mocks
const vscode = mockVscode;
global.vscode = vscode;
// Import types and services after mocking
const QaxNextEditTypes_1 = require("../../qaxNextEdit/types/QaxNextEditTypes");
const NextEditTypes_1 = require("../types/NextEditTypes");
// Test counter
let testCount = 0;
let passedCount = 0;
let failedCount = 0;
function test(name, fn) {
    testCount++;
    console.log(`\n🧪 Integration Test ${testCount}: ${name}`);
    try {
        const result = fn();
        if (result instanceof Promise) {
            return result.then(() => {
                console.log(`✅ PASSED: ${name}`);
                passedCount++;
            }).catch((error) => {
                console.log(`❌ FAILED: ${name}`);
                console.log(`   Error: ${error.message}`);
                failedCount++;
            });
        }
        else {
            console.log(`✅ PASSED: ${name}`);
            passedCount++;
        }
    }
    catch (error) {
        console.log(`❌ FAILED: ${name}`);
        console.log(`   Error: ${error.message}`);
        failedCount++;
    }
}
async function runIntegrationTests() {
    console.log("🚀 Starting QaxNextEdit Integration Tests");
    console.log("=".repeat(50));
    // Test 1: Configuration detection
    test("Should detect QaxNextEdit configuration", () => {
        const config = vscode.workspace.getConfiguration("qax-code.nextEdit");
        const useQaxNextEdit = config.get("useQaxNextEdit", false);
        assert.strictEqual(useQaxNextEdit, true);
    });
    // Test 2: QaxJumpSuggestion to NextEditSuggestion conversion
    test("Should convert QaxJumpSuggestion to NextEditSuggestion format", () => {
        // Create mock QaxJumpSuggestion
        const qaxSuggestion = {
            id: "test-suggestion-1",
            filePath: "test.ts",
            range: new vscode.Range({ line: 0, character: 4 }, { line: 0, character: 11 }),
            description: "Update variable name from 'oldName' to 'newName'",
            changeType: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
            priority: 8,
            suggestedEdit: {
                range: new vscode.Range({ line: 0, character: 4 }, { line: 0, character: 11 }),
                newText: "newName",
                description: "Rename 'oldName' to 'newName'"
            },
            relatedChange: {
                type: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
                filePath: "test.ts",
                range: new vscode.Range({ line: 0, character: 4 }, { line: 0, character: 11 }),
                oldValue: "oldName",
                newValue: "newName",
                confidence: 0.9
            }
        };
        // Mock NextEditService conversion method
        const convertSuggestion = (qaxSuggestion) => {
            let type = NextEditTypes_1.NextEditType.MODIFY;
            let description = qaxSuggestion.description;
            if (qaxSuggestion.relatedChange) {
                const change = qaxSuggestion.relatedChange;
                if (change.type === "variable_rename" || change.type === "function_parameter_change") {
                    type = NextEditTypes_1.NextEditType.MODIFY;
                    description = `Change: ${change.oldValue} ➜ ${change.newValue || "updated"}`;
                }
                else if (change.type === "function_call_deletion" || change.type === "variable_deletion") {
                    type = NextEditTypes_1.NextEditType.DELETE;
                    description = `Remove: ${change.oldValue}`;
                }
            }
            const anchor = `line_${qaxSuggestion.range.start.line + 1}_char_${qaxSuggestion.range.start.character}`;
            return {
                id: qaxSuggestion.id || `qax_suggestion_0`,
                type,
                description,
                location: {
                    anchor,
                    position: type === NextEditTypes_1.NextEditType.DELETE ? "replace" : (type === NextEditTypes_1.NextEditType.ADD ? "after" : "replace")
                },
                patch: {
                    oldContent: "",
                    newContent: qaxSuggestion.suggestedEdit?.newText || ""
                },
                reasoning: `QaxNextEdit detected ${qaxSuggestion.changeType} with ${Math.round((qaxSuggestion.relatedChange?.confidence || 0.8) * 100)}% confidence`,
                filePath: qaxSuggestion.filePath,
                createdAt: new Date()
            };
        };
        const converted = convertSuggestion(qaxSuggestion);
        // Verify conversion
        assert.strictEqual(converted.id, "test-suggestion-1");
        assert.strictEqual(converted.type, NextEditTypes_1.NextEditType.MODIFY);
        assert.strictEqual(converted.description, "Change: oldName ➜ newName");
        assert.strictEqual(converted.location.anchor, "line_1_char_4");
        assert.strictEqual(converted.location.position, "replace");
        assert.strictEqual(converted.patch.newContent, "newName");
        assert.ok(converted.reasoning.includes("90% confidence"));
        assert.strictEqual(converted.filePath, "test.ts");
    });
    // Test 3: Different change types conversion
    test("Should handle different change types correctly", () => {
        const testCases = [
            {
                changeType: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
                oldValue: "oldVar",
                newValue: "newVar",
                expectedType: NextEditTypes_1.NextEditType.MODIFY,
                expectedDescription: "Change: oldVar ➜ newVar"
            },
            {
                changeType: QaxNextEditTypes_1.QaxChangeType.FUNCTION_CALL_DELETION,
                oldValue: "deletedFunction()",
                newValue: undefined,
                expectedType: NextEditTypes_1.NextEditType.DELETE,
                expectedDescription: "Remove: deletedFunction()"
            },
            {
                changeType: QaxNextEditTypes_1.QaxChangeType.VARIABLE_DELETION,
                oldValue: "deletedVar",
                newValue: undefined,
                expectedType: NextEditTypes_1.NextEditType.DELETE,
                expectedDescription: "Remove: deletedVar"
            }
        ];
        testCases.forEach((testCase, index) => {
            const qaxSuggestion = {
                id: `test-${index}`,
                filePath: "test.ts",
                range: new vscode.Range({ line: 0, character: 0 }, { line: 0, character: 10 }),
                description: "Test suggestion",
                changeType: testCase.changeType,
                priority: 5,
                relatedChange: {
                    type: testCase.changeType,
                    filePath: "test.ts",
                    range: new vscode.Range({ line: 0, character: 0 }, { line: 0, character: 10 }),
                    oldValue: testCase.oldValue,
                    newValue: testCase.newValue,
                    confidence: 0.8
                }
            };
            // Mock conversion
            const convertSuggestion = (qaxSuggestion) => {
                let type = NextEditTypes_1.NextEditType.MODIFY;
                let description = qaxSuggestion.description;
                if (qaxSuggestion.relatedChange) {
                    const change = qaxSuggestion.relatedChange;
                    if (change.type === "variable_rename" || change.type === "function_parameter_change") {
                        type = NextEditTypes_1.NextEditType.MODIFY;
                        description = `Change: ${change.oldValue} ➜ ${change.newValue || "updated"}`;
                    }
                    else if (change.type === "function_call_deletion" || change.type === "variable_deletion") {
                        type = NextEditTypes_1.NextEditType.DELETE;
                        description = `Remove: ${change.oldValue}`;
                    }
                }
                return { type, description };
            };
            const converted = convertSuggestion(qaxSuggestion);
            assert.strictEqual(converted.type, testCase.expectedType, `Failed for ${testCase.changeType}`);
            assert.strictEqual(converted.description, testCase.expectedDescription, `Failed for ${testCase.changeType}`);
        });
    });
    // Test 4: Anchor generation
    test("Should generate appropriate anchors", () => {
        const testRanges = [
            { line: 0, character: 0, expected: "line_1_char_0" },
            { line: 5, character: 10, expected: "line_6_char_10" },
            { line: 99, character: 50, expected: "line_100_char_50" }
        ];
        testRanges.forEach(testRange => {
            const range = new vscode.Range({ line: testRange.line, character: testRange.character }, { line: testRange.line, character: testRange.character + 10 });
            const anchor = `line_${range.start.line + 1}_char_${range.start.character}`;
            assert.strictEqual(anchor, testRange.expected);
        });
    });
    // Test 5: Complete workflow simulation
    test("Should handle complete workflow", async () => {
        // Simulate document with changes
        const document = await vscode.workspace.openTextDocument({
            content: "let newName = 5;\nconsole.log(newName);",
            language: "typescript"
        });
        assert.ok(document);
        assert.strictEqual(document.languageId, "typescript");
        // Simulate QaxNextEdit suggestions
        const mockSuggestions = [
            {
                id: "workflow-test-1",
                filePath: document.uri.fsPath,
                range: new vscode.Range({ line: 1, character: 12 }, { line: 1, character: 19 }),
                description: "Update variable reference",
                changeType: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
                priority: 9,
                suggestedEdit: {
                    range: new vscode.Range({ line: 1, character: 12 }, { line: 1, character: 19 }),
                    newText: "newName",
                    description: "Update variable reference"
                },
                relatedChange: {
                    type: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
                    filePath: document.uri.fsPath,
                    range: new vscode.Range({ line: 1, character: 12 }, { line: 1, character: 19 }),
                    oldValue: "oldName",
                    newValue: "newName",
                    confidence: 0.95
                }
            }
        ];
        // Verify suggestions structure
        assert.strictEqual(mockSuggestions.length, 1);
        assert.strictEqual(mockSuggestions[0].changeType, QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME);
        assert.strictEqual(mockSuggestions[0].priority, 9);
        assert.ok(mockSuggestions[0].relatedChange);
        assert.strictEqual(mockSuggestions[0].relatedChange.confidence, 0.95);
    });
    // Wait for any async operations
    await new Promise(resolve => setTimeout(resolve, 100));
    // Print results
    console.log("\n" + "=".repeat(50));
    console.log("📊 QaxNextEdit Integration Test Results:");
    console.log(`  ✅ Passed: ${passedCount}`);
    console.log(`  ❌ Failed: ${failedCount}`);
    console.log(`  📈 Success Rate: ${Math.round((passedCount / testCount) * 100)}%`);
    if (failedCount === 0) {
        console.log("\n🎉 All integration tests passed!");
        console.log("🔗 QaxNextEdit integration is working correctly!");
        return true;
    }
    else {
        console.log(`\n💥 ${failedCount} integration test(s) failed!`);
        return false;
    }
}
// Run integration tests
runIntegrationTests().then((success) => {
    process.exit(success ? 0 : 1);
}).catch((error) => {
    console.error("💥 Error running integration tests:", error);
    process.exit(1);
});
