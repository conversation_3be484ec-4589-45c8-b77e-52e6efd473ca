import { ToolDefinition } from "@core/prompts/model_prompts/jsonToolToXml"
import crypto from "crypto"

export const updateTodoListToolName = "UpdateTodoList"

const descriptionForAgent = `Replace the entire TODO list with an updated checklist reflecting the current state. You MUST give full list, Unit test and Documentation MUST be included in the list; the system will overwrite the previous one. This tool is designed for step-by-step task tracking, allowing you to confirm completion of each step before updating, update multiple task statuses at once (e.g., mark one as completed and start the next), and dynamically add new todos discovered during long or complex tasks.

**When to Use:**
- The task involves multiple steps or requires ongoing tracking
- You need to update the status of several todos at once
- New actionable items are discovered during task execution
- The user requests a todo list or provides multiple tasks
- The task is complex and benefits from clear, stepwise progress tracking

**When NOT to Use:**
- There is only a single, trivial task
- The task can be completed in one or two simple steps
- The request is purely conversational or informational

**Checklist Format:**
- Use a single-level markdown checklist (no nesting or subtasks)
- List todos in the intended execution order
- Status options:
  [ ] Task description (pending)
  [x] Task description (completed)
  [-] Task description (in progress)

**Core Principles:**
- tasks should mainly about software design, implementation and test
- Before updating, always confirm which todos have been completed since the last update
- You may update multiple statuses in a single update (e.g., mark the previous as completed and the next as in progress)
- When a new actionable item is discovered during a long or complex task, add it to the todo list immediately
- Do not remove any unfinished todos unless explicitly instructed
- Always retain all unfinished tasks, updating their status as needed
- Only mark a task as completed when it is fully accomplished (no partials, no unresolved dependencies)
- If a task is blocked, keep it as in_progress and add a new todo describing what needs to be resolved
- Remove tasks only if they are no longer relevant or if the user requests deletion`

export const updateTodoListToolDefinition: ToolDefinition = {
	name: updateTodoListToolName,
	descriptionForAgent,
	inputSchema: {
		type: "object",
		properties: {
			todos: {
				type: "string",
				description:
					"The complete todo list as a markdown checklist. Use [ ] for pending, [x] for completed, and [-] for in progress. Always provide the full list.",
			},
		},
		required: ["todos"],
	},
}

// Types for todo management
export type TodoStatus = "pending" | "in_progress" | "completed"

export interface TodoItem {
	id: string
	content: string
	status: TodoStatus
}

// Global state for approved todo list
let approvedTodoList: TodoItem[] | undefined = undefined

/**
 * Convert TodoItem[] to markdown checklist string.
 */
export function todoListToMarkdown(todos: TodoItem[]): string {
	return todos
		.map((t) => {
			let box = "[ ]"
			if (t.status === "completed") box = "[x]"
			else if (t.status === "in_progress") box = "[-]"
			return `${box} ${t.content}`
		})
		.join("\n")
}

/**
 * Parse markdown checklist string to TodoItem[].
 */
export function parseMarkdownChecklist(md: string): TodoItem[] {
	if (typeof md !== "string") return []
	const lines = md
		.split(/\r?\n/)
		.map((l) => l.trim())
		.filter(Boolean)
	const todos: TodoItem[] = []
	for (const line of lines) {
		const match = line.match(/^\[\s*([ xX\-~])\s*\]\s+(.+)$/)
		if (!match) continue
		let status: TodoStatus = "pending"
		if (match[1] === "x" || match[1] === "X") status = "completed"
		else if (match[1] === "-" || match[1] === "~") status = "in_progress"
		// ID 只基于任务内容生成，不包含状态，确保状态变化时 ID 不变
		const id = crypto.createHash("md5").update(match[2]).digest("hex")
		todos.push({
			id,
			content: match[2],
			status,
		})
	}
	return todos
}

/**
 * Validate todo items array.
 */
export function validateTodos(todos: TodoItem[]): { valid: boolean; error?: string } {
	if (!Array.isArray(todos)) {
		return { valid: false, error: "todos must be an array" }
	}
	for (const todo of todos) {
		if (!todo.id || !todo.content || !todo.status) {
			return { valid: false, error: "Each todo must have id, content, and status" }
		}
		if (!["pending", "in_progress", "completed"].includes(todo.status)) {
			return { valid: false, error: "Invalid todo status" }
		}
	}
	return { valid: true }
}

/**
 * Normalize todo status.
 */
export function normalizeStatus(status: string): TodoStatus {
	if (status === "completed" || status === "in_progress") return status as TodoStatus
	return "pending"
}

/**
 * Set pending todo list for approval process.
 */
export function setPendingTodoList(todos: TodoItem[]) {
	approvedTodoList = todos
}

/**
 * Get approved todo list.
 */
export function getApprovedTodoList(): TodoItem[] | undefined {
	return approvedTodoList
}

// Task integration functions (will be imported by Task class)

/**
 * Add a todo item to the task's todoList.
 */
export function addTodoToTask(task: any, content: string, status: TodoStatus = "pending", id?: string): TodoItem {
	const todo: TodoItem = {
		id: id ?? crypto.randomUUID(),
		content,
		status,
	}
	if (!task.todoList) task.todoList = []
	task.todoList.push(todo)
	return todo
}

/**
 * Update the status of a todo item by id.
 */
export function updateTodoStatusForTask(task: any, id: string, nextStatus: TodoStatus): boolean {
	if (!task.todoList) return false
	const idx = task.todoList.findIndex((t: TodoItem) => t.id === id)
	if (idx === -1) return false
	const current = task.todoList[idx]
	if (
		(current.status === "pending" && nextStatus === "in_progress") ||
		(current.status === "in_progress" && nextStatus === "completed") ||
		current.status === nextStatus
	) {
		task.todoList[idx] = { ...current, status: nextStatus }
		return true
	}
	return false
}

/**
 * Remove a todo item by id.
 */
export function removeTodoFromTask(task: any, id: string): boolean {
	if (!task.todoList) return false
	const idx = task.todoList.findIndex((t: TodoItem) => t.id === id)
	if (idx === -1) return false
	task.todoList.splice(idx, 1)
	return true
}

/**
 * Get a copy of the todoList.
 */
export function getTodoListForTask(task: any): TodoItem[] | undefined {
	return task.todoList?.slice()
}

/**
 * Set the todoList for the task.
 */
export async function setTodoListForTask(task?: any, todos?: TodoItem[]) {
	if (task === undefined) return
	task.todoList = Array.isArray(todos) ? todos : []
}

/**
 * Restore the todoList from argument or from clineMessages.
 */
export function restoreTodoListForTask(task: any, todoList?: TodoItem[]) {
	if (todoList) {
		task.todoList = Array.isArray(todoList) ? todoList : []
		return
	}
	// Import getLatestTodo dynamically to avoid circular dependency
	const { getLatestTodo } = require("@shared/todo")
	task.todoList = getLatestTodo(task.clineMessages)
}

/**
 * Main tool execution function for update_todo_list
 * Note: This function is only called when block.partial is false (complete content)
 */
export async function updateTodoListTool(
	taskState: any,
	block: any,
	handleError: any,
	pushToolResult: any,
	userEdited?: boolean,
) {
	// If userEdited is true, only show "User Edit Succeeded" and do nothing else
	if (userEdited === true) {
		pushToolResult("User Edit Succeeded")
		return
	}

	try {
		const todosRaw = block.params.todos

		let todos: TodoItem[]
		try {
			todos = parseMarkdownChecklist(todosRaw || "")
		} catch {
			taskState.consecutiveMistakeCount++
			pushToolResult("The todos parameter is not valid markdown checklist")
			return
		}

		const { valid, error } = validateTodos(todos)
		if (!valid) {
			taskState.consecutiveMistakeCount++
			pushToolResult(error || "todos parameter validation failed")
			return
		}

		let normalizedTodos: TodoItem[] = todos.map((t) => ({
			id: t.id,
			content: t.content,
			status: normalizeStatus(t.status),
		}))

		// Reset consecutive mistake count on successful parsing
		taskState.consecutiveMistakeCount = 0

		// Update task's todo list directly without user approval
		await setTodoListForTask(taskState, normalizedTodos)

		// Generate result message
		const todoCount = normalizedTodos.length
		const completedCount = normalizedTodos.filter((t) => t.status === "completed").length
		const inProgressCount = normalizedTodos.filter((t) => t.status === "in_progress").length
		const pendingCount = normalizedTodos.filter((t) => t.status === "pending").length

		let resultMessage = `Todo list updated successfully with ${todoCount} item${todoCount !== 1 ? "s" : ""}.`

		if (todoCount > 0) {
			const statusParts = []
			if (pendingCount > 0) statusParts.push(`${pendingCount} pending`)
			if (inProgressCount > 0) statusParts.push(`${inProgressCount} in progress`)
			if (completedCount > 0) statusParts.push(`${completedCount} completed`)

			if (statusParts.length > 0) {
				resultMessage += ` Status: ${statusParts.join(", ")}.`
			}
		}

		pushToolResult(resultMessage) // Allow subsequent tools to execute
	} catch (error) {
		await handleError("updating todo list", error, block)
	}
}
