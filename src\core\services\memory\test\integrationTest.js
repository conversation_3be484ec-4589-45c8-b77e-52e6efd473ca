/**
 * Integration test for compiled TypeScript unified memory processing
 */

const fs = require("fs").promises
const path = require("path")
const os = require("os")

// Import compiled modules - using local constants instead of imports to avoid dependency issues
// const { MemoryManager } = require('../../../../../out/src/core/services/memory/MemoryManager');
// const { MemoryCategory, MemoryUpdateOperation } = require('../../../../../out/src/core/services/memory/types');

// Mock the required types
const MemoryCategory = {
	TOOL_PREFERENCES: "tool_preferences",
	TECH_STACK: "tech_stack",
	CODE_STYLE: "code_style",
	WORKFLOW: "workflow",
	GENERAL: "general",
}

const MemoryUpdateOperation = {
	ADD: "add",
	UPDATE: "update",
	REMOVE: "remove",
	MERGE: "merge",
}

// Mock API handler that implements the required interface
class IntegrationMockApiHandler {
	async *createMessage(systemPrompt, messages) {
		// Extract input from messages
		const userMessage = messages.find((m) => m.role === "user")?.content || ""
		const inputMatch = userMessage.match(/用户新输入：\s*"""\s*(.*?)\s*"""/s)
		const input = inputMatch ? inputMatch[1] : "default"

		console.log(`🤖 Processing: "${input.substring(0, 50)}..."`)

		// Generate response based on input content
		let response

		if (input.includes("React") && input.includes("TypeScript")) {
			response = {
				shouldUpdate: true,
				instructions: [
					{
						operation: MemoryUpdateOperation.ADD,
						category: MemoryCategory.TOOL_PREFERENCES,
						newEntry: {
							summary: "用户偏好使用 React 和 TypeScript 开发",
							confidence: 0.9,
							reasoning: "明确表达了技术栈偏好",
						},
					},
				],
				reasoning: "用户明确表达了技术栈偏好，应该记录",
			}
		} else if (input.includes("Vue")) {
			response = {
				shouldUpdate: true,
				instructions: [
					{
						operation: MemoryUpdateOperation.ADD,
						category: MemoryCategory.TOOL_PREFERENCES,
						newEntry: {
							summary: "用户偏好使用 Vue.js 开发",
							confidence: 0.85,
							reasoning: "表达了对 Vue.js 的偏好",
						},
					},
				],
				reasoning: "用户表达了对 Vue.js 的偏好",
			}
		} else if (input.includes("函数式编程")) {
			response = {
				shouldUpdate: true,
				instructions: [
					{
						operation: MemoryUpdateOperation.ADD,
						category: MemoryCategory.CODE_STYLE,
						newEntry: {
							summary: "用户偏好函数式编程风格",
							confidence: 0.8,
							reasoning: "明确的编程风格偏好",
						},
					},
				],
				reasoning: "用户表达了编程风格偏好",
			}
		} else if (input.includes("Git Flow")) {
			response = {
				shouldUpdate: true,
				instructions: [
					{
						operation: MemoryUpdateOperation.ADD,
						category: MemoryCategory.WORKFLOW,
						newEntry: {
							summary: "用户习惯使用 Git Flow 工作流",
							confidence: 0.9,
							reasoning: "明确的工作流程偏好",
						},
					},
				],
				reasoning: "用户表达了工作流程偏好",
			}
		} else if (input.includes("更新") && input.includes("React")) {
			response = {
				shouldUpdate: true,
				instructions: [
					{
						operation: MemoryUpdateOperation.UPDATE,
						category: MemoryCategory.TOOL_PREFERENCES,
						targetId: "mock-react-id",
						newEntry: {
							summary: "用户更新了对 React 的偏好",
							confidence: 0.85,
							reasoning: "更新现有偏好",
						},
					},
				],
				reasoning: "用户更新了现有的技术偏好",
			}
		} else {
			response = {
				shouldUpdate: false,
				instructions: [],
				reasoning: "输入内容与开发相关记忆无关，无需更新",
			}
		}

		const jsonResponse = `\`\`\`json\n${JSON.stringify(response, null, 2)}\n\`\`\``
		yield { type: "text", text: jsonResponse }
	}
}

async function runIntegrationTests() {
	console.log("🚀 Running TypeScript Integration Tests...")
	console.log("=".repeat(60))

	let testsPassed = 0
	let testsFailed = 0

	// Create temporary test workspace
	const testWorkspaceRoot = await fs.mkdtemp(path.join(os.tmpdir(), "memory-integration-"))
	const clineruleDir = path.join(testWorkspaceRoot, ".clinerules")
	await fs.mkdir(clineruleDir, { recursive: true })

	try {
		const mockApiHandler = new IntegrationMockApiHandler()

		// Test 1: MemoryManager instantiation
		console.log("\n🧪 Test 1: MemoryManager instantiation")
		try {
			const memoryManager = new MemoryManager(testWorkspaceRoot, mockApiHandler, {
				enabled: true,
				useUnifiedProcessing: true,
				minInputLength: 1,
				debounceMs: 50,
			})

			if (memoryManager) {
				console.log("✅ Test 1 passed: MemoryManager created successfully")
				testsPassed++
			} else {
				console.log("❌ Test 1 failed: MemoryManager not created")
				testsFailed++
			}
		} catch (error) {
			console.log("❌ Test 1 failed with error:", error.message)
			testsFailed++
		}

		// Test 2: Unified processing - ADD operation
		console.log("\n🧪 Test 2: Unified processing - ADD operation")
		try {
			const memoryManager = new MemoryManager(testWorkspaceRoot, mockApiHandler, {
				enabled: true,
				useUnifiedProcessing: true,
				minInputLength: 1,
				debounceMs: 50,
			})

			await memoryManager.processUserInputUnified("我喜欢使用 React 和 TypeScript 开发前端应用")

			// Wait for processing
			await new Promise((resolve) => setTimeout(resolve, 200))

			const memories = memoryManager.getAllMemories()
			const toolPreferences = memories[MemoryCategory.TOOL_PREFERENCES]

			if (toolPreferences && toolPreferences.length > 0) {
				console.log("✅ Test 2 passed: ADD operation successful")
				console.log(`   📝 Added: ${toolPreferences[0].summary}`)
				testsPassed++
			} else {
				console.log("❌ Test 2 failed: No memory entry added")
				testsFailed++
			}
		} catch (error) {
			console.log("❌ Test 2 failed with error:", error.message)
			testsFailed++
		}

		// Test 3: Different category - CODE_STYLE
		console.log("\n🧪 Test 3: Different category - CODE_STYLE")
		try {
			const memoryManager = new MemoryManager(testWorkspaceRoot, mockApiHandler, {
				enabled: true,
				useUnifiedProcessing: true,
				minInputLength: 1,
				debounceMs: 50,
			})

			await memoryManager.processUserInputUnified("我喜欢使用函数式编程风格")

			// Wait for processing
			await new Promise((resolve) => setTimeout(resolve, 200))

			const memories = memoryManager.getAllMemories()
			const codeStyle = memories[MemoryCategory.CODE_STYLE]

			if (codeStyle && codeStyle.length > 0) {
				console.log("✅ Test 3 passed: CODE_STYLE category works")
				console.log(`   📝 Added: ${codeStyle[0].summary}`)
				testsPassed++
			} else {
				console.log("❌ Test 3 failed: CODE_STYLE entry not added")
				testsFailed++
			}
		} catch (error) {
			console.log("❌ Test 3 failed with error:", error.message)
			testsFailed++
		}

		// Test 4: WORKFLOW category
		console.log("\n🧪 Test 4: WORKFLOW category")
		try {
			const memoryManager = new MemoryManager(testWorkspaceRoot, mockApiHandler, {
				enabled: true,
				useUnifiedProcessing: true,
				minInputLength: 1,
				debounceMs: 50,
			})

			await memoryManager.processUserInputUnified("我习惯使用 Git Flow 工作流进行开发")

			// Wait for processing
			await new Promise((resolve) => setTimeout(resolve, 200))

			const memories = memoryManager.getAllMemories()
			const workflow = memories[MemoryCategory.WORKFLOW]

			if (workflow && workflow.length > 0) {
				console.log("✅ Test 4 passed: WORKFLOW category works")
				console.log(`   📝 Added: ${workflow[0].summary}`)
				testsPassed++
			} else {
				console.log("❌ Test 4 failed: WORKFLOW entry not added")
				testsFailed++
			}
		} catch (error) {
			console.log("❌ Test 4 failed with error:", error.message)
			testsFailed++
		}

		// Test 5: No update scenario
		console.log("\n🧪 Test 5: No update scenario")
		try {
			const memoryManager = new MemoryManager(testWorkspaceRoot, mockApiHandler, {
				enabled: true,
				useUnifiedProcessing: true,
				minInputLength: 1,
				debounceMs: 50,
			})

			const beforeMemories = memoryManager.getAllMemories()
			const beforeCount = Object.values(beforeMemories).reduce((sum, arr) => sum + arr.length, 0)

			await memoryManager.processUserInputUnified("今天天气真好，阳光明媚")

			// Wait for processing
			await new Promise((resolve) => setTimeout(resolve, 200))

			const afterMemories = memoryManager.getAllMemories()
			const afterCount = Object.values(afterMemories).reduce((sum, arr) => sum + arr.length, 0)

			if (beforeCount === afterCount) {
				console.log("✅ Test 5 passed: No unnecessary memory created")
				testsPassed++
			} else {
				console.log("❌ Test 5 failed: Unnecessary memory was created")
				testsFailed++
			}
		} catch (error) {
			console.log("❌ Test 5 failed with error:", error.message)
			testsFailed++
		}

		// Test 6: File persistence
		console.log("\n🧪 Test 6: File persistence")
		try {
			const memoriesPath = path.join(testWorkspaceRoot, ".clinerules", "memories.md")
			const exists = await fs
				.access(memoriesPath)
				.then(() => true)
				.catch(() => false)

			if (exists) {
				const content = await fs.readFile(memoriesPath, "utf-8")
				if (content.includes("React") || content.includes("函数式") || content.includes("Git Flow")) {
					console.log("✅ Test 6 passed: Memory file persisted correctly")
					console.log(`   📄 File size: ${content.length} characters`)
					testsPassed++
				} else {
					console.log("❌ Test 6 failed: Memory file missing expected content")
					console.log(`   📄 Content: ${content.substring(0, 100)}...`)
					testsFailed++
				}
			} else {
				console.log("❌ Test 6 failed: Memory file not created")
				testsFailed++
			}
		} catch (error) {
			console.log("❌ Test 6 failed with error:", error.message)
			testsFailed++
		}
	} finally {
		// Cleanup
		await fs.rm(testWorkspaceRoot, { recursive: true, force: true })
	}

	// Results
	console.log("\n" + "=".repeat(60))
	console.log("📊 Integration Test Results:")
	console.log(`✅ Passed: ${testsPassed}`)
	console.log(`❌ Failed: ${testsFailed}`)
	console.log(`📈 Total: ${testsPassed + testsFailed}`)
	console.log(`🎯 Success Rate: ${((testsPassed / (testsPassed + testsFailed)) * 100).toFixed(1)}%`)

	if (testsFailed === 0) {
		console.log("\n🎉 All integration tests passed!")
		console.log("✨ Unified memory processing is fully functional!")
		console.log("🚀 Ready for production use!")
	} else {
		console.log(`\n⚠️  ${testsFailed} integration test(s) failed.`)
		return false
	}

	return true
}

// Run tests
if (require.main === module) {
	runIntegrationTests()
		.then((success) => {
			process.exit(success ? 0 : 1)
		})
		.catch((error) => {
			console.error("\n💥 Integration test suite failed:", error.message)
			console.error("Stack:", error.stack)
			process.exit(1)
		})
}

module.exports = { runIntegrationTests }
