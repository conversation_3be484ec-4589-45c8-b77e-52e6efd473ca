// VS Code API mock for testing

const EventEmitter = require('events')

class MockDisposable {
	constructor(fn) {
		this.fn = fn
	}
	
	dispose() {
		if (this.fn) {
			this.fn()
		}
	}
}

class MockRange {
	constructor(startLine, startChar, endLine, endChar) {
		this.start = new MockPosition(startLine, startChar)
		this.end = new MockPosition(endLine, endChar)
	}
	
	get isEmpty() {
		return this.start.line === this.end.line && this.start.character === this.end.character
	}
	
	get isSingleLine() {
		return this.start.line === this.end.line
	}
	
	isEqual(other) {
		return this.start.isEqual(other.start) && this.end.isEqual(other.end)
	}
	
	intersection(other) {
		const startLine = Math.max(this.start.line, other.start.line)
		const endLine = Math.min(this.end.line, other.end.line)
		
		if (startLine > endLine) return null
		
		if (startLine === endLine) {
			const startChar = startLine === this.start.line ? 
				Math.max(this.start.character, other.start.character) : 
				other.start.character
			const endChar = endLine === this.end.line ? 
				Math.min(this.end.character, other.end.character) : 
				this.end.character
			
			if (startChar >= endChar) return null
			
			return new MockRange(startLine, startChar, endLine, endChar)
		}
		
		return new MockRange(startLine, 0, endLine, 0)
	}
}

class MockPosition {
	constructor(line, character) {
		this.line = line
		this.character = character
	}
	
	isEqual(other) {
		return this.line === other.line && this.character === other.character
	}
	
	isBefore(other) {
		return this.line < other.line || (this.line === other.line && this.character < other.character)
	}
	
	isAfter(other) {
		return this.line > other.line || (this.line === other.line && this.character > other.character)
	}
}

class MockSelection extends MockRange {
	constructor(anchorLine, anchorChar, activeLine, activeChar) {
		super(anchorLine, anchorChar, activeLine, activeChar)
		this.anchor = new MockPosition(anchorLine, anchorChar)
		this.active = new MockPosition(activeLine, activeChar)
	}
	
	get isReversed() {
		return this.anchor.isAfter(this.active)
	}
}

class MockLocation {
	constructor(uri, range) {
		this.uri = uri
		this.range = range
	}
}

class MockUri {
	constructor(scheme, authority, path, query, fragment) {
		this.scheme = scheme
		this.authority = authority
		this.path = path
		this.query = query
		this.fragment = fragment
		this.fsPath = path
	}
	
	static file(path) {
		return new MockUri('file', '', path, '', '')
	}
	
	static parse(value) {
		// Simple URI parsing
		const url = new URL(value)
		return new MockUri(url.protocol.slice(0, -1), url.hostname, url.pathname, url.search, url.hash)
	}
}

class MockThemeColor {
	constructor(id) {
		this.id = id
	}
}

class MockMarkdownString {
	constructor(value) {
		this.value = value || ''
		this.isTrusted = false
	}
	
	appendMarkdown(value) {
		this.value += value
		return this
	}
	
	appendCodeblock(value, language) {
		this.value += `\n\`\`\`${language || ''}\n${value}\n\`\`\`\n`
		return this
	}
}

class MockHover {
	constructor(contents, range) {
		this.contents = Array.isArray(contents) ? contents : [contents]
		this.range = range
	}
}

const mockCommands = new Map()

const vscode = {
	// Classes
	Range: MockRange,
	Position: MockPosition,
	Selection: MockSelection,
	Location: MockLocation,
	Uri: MockUri,
	ThemeColor: MockThemeColor,
	MarkdownString: MockMarkdownString,
	Hover: MockHover,
	Disposable: MockDisposable,
	
	// Enums
	StatusBarAlignment: {
		Left: 1,
		Right: 2
	},
	
	TextEditorRevealType: {
		Default: 0,
		InCenter: 1,
		InCenterIfOutsideViewport: 2,
		AtTop: 3
	},
	
	SymbolKind: {
		File: 0,
		Module: 1,
		Namespace: 2,
		Package: 3,
		Class: 4,
		Method: 5,
		Property: 6,
		Field: 7,
		Constructor: 8,
		Enum: 9,
		Interface: 10,
		Function: 11,
		Variable: 12,
		Constant: 13,
		String: 14,
		Number: 15,
		Boolean: 16,
		Array: 17,
		Object: 18,
		Key: 19,
		Null: 20,
		EnumMember: 21,
		Struct: 22,
		Event: 23,
		Operator: 24,
		TypeParameter: 25
	},
	
	ConfigurationTarget: {
		Global: 1,
		Workspace: 2,
		WorkspaceFolder: 3
	},
	
	// API namespaces
	workspace: {
		onDidChangeTextDocument: jest.fn(() => new MockDisposable()),
		onDidOpenTextDocument: jest.fn(() => new MockDisposable()),
		onDidCloseTextDocument: jest.fn(() => new MockDisposable()),
		onDidChangeConfiguration: jest.fn(() => new MockDisposable()),
		getConfiguration: jest.fn(() => ({
			get: jest.fn((key, defaultValue) => defaultValue),
			update: jest.fn()
		})),
		openTextDocument: jest.fn(),
		findTextInFiles: jest.fn(() => Promise.resolve(new Map()))
	},
	
	window: {
		onDidChangeActiveTextEditor: jest.fn(() => new MockDisposable()),
		createStatusBarItem: jest.fn(() => ({
			text: '',
			tooltip: '',
			command: '',
			show: jest.fn(),
			hide: jest.fn(),
			dispose: jest.fn()
		})),
		createTextEditorDecorationType: jest.fn(() => ({
			dispose: jest.fn()
		})),
		showTextDocument: jest.fn(),
		showInformationMessage: jest.fn(),
		showWarningMessage: jest.fn(),
		showErrorMessage: jest.fn(),
		showQuickPick: jest.fn(),
		activeTextEditor: null
	},
	
	languages: {
		onDidChangeDiagnostics: jest.fn(() => new MockDisposable()),
		registerHoverProvider: jest.fn(() => new MockDisposable())
	},
	
	commands: {
		executeCommand: jest.fn((command, ...args) => {
			const handler = mockCommands.get(command)
			return handler ? handler(...args) : Promise.resolve(null)
		}),
		registerCommand: jest.fn((command, handler) => {
			mockCommands.set(command, handler)
			return new MockDisposable(() => mockCommands.delete(command))
		})
	},
	
	// Test utilities
	__setMockCommand: (command, handler) => {
		mockCommands.set(command, handler)
	},
	
	__clearMockCommands: () => {
		mockCommands.clear()
	}
}

module.exports = vscode
