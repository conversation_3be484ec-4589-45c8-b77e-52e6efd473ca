#!/usr/bin/env node

const { execSync } = require('child_process')
const path = require('path')
const fs = require('fs')

// Colors for console output
const colors = {
	reset: '\x1b[0m',
	bright: '\x1b[1m',
	red: '\x1b[31m',
	green: '\x1b[32m',
	yellow: '\x1b[33m',
	blue: '\x1b[34m',
	magenta: '\x1b[35m',
	cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
	console.log(`${colors[color]}${message}${colors.reset}`)
}

function runCommand(command, options = {}) {
	try {
		const result = execSync(command, {
			stdio: 'pipe',
			encoding: 'utf8',
			...options
		})
		return { success: true, output: result }
	} catch (error) {
		return { success: false, output: error.stdout || error.message, error }
	}
}

async function main() {
	log('🚀 Running QaxNextEdit Tests', 'cyan')
	log('=' * 50, 'cyan')
	
	const testDir = __dirname
	const projectRoot = path.resolve(testDir, '../../../../..')
	
	// Check if Jest is available
	log('📦 Checking dependencies...', 'blue')
	const jestCheck = runCommand('npx jest --version', { cwd: projectRoot })
	if (!jestCheck.success) {
		log('❌ Jest not found. Installing dependencies...', 'yellow')
		const installResult = runCommand('npm install --save-dev jest ts-jest @types/jest', { cwd: projectRoot })
		if (!installResult.success) {
			log('❌ Failed to install Jest dependencies', 'red')
			log(installResult.output, 'red')
			process.exit(1)
		}
		log('✅ Dependencies installed', 'green')
	} else {
		log('✅ Jest is available', 'green')
	}
	
	// Run TypeScript compilation check
	log('🔍 Checking TypeScript compilation...', 'blue')
	const tscCheck = runCommand('npx tsc --noEmit --skipLibCheck', { cwd: projectRoot })
	if (!tscCheck.success) {
		log('⚠️  TypeScript compilation warnings:', 'yellow')
		log(tscCheck.output, 'yellow')
	} else {
		log('✅ TypeScript compilation passed', 'green')
	}
	
	// Run tests
	log('🧪 Running tests...', 'blue')
	const testCommand = `npx jest --config="${path.join(testDir, 'jest.config.js')}" --verbose --coverage`
	const testResult = runCommand(testCommand, { cwd: projectRoot })
	
	if (testResult.success) {
		log('✅ All tests passed!', 'green')
		log(testResult.output, 'green')
		
		// Check coverage
		const coverageDir = path.join(testDir, 'coverage')
		if (fs.existsSync(coverageDir)) {
			log('📊 Coverage report generated in:', 'blue')
			log(coverageDir, 'cyan')
		}
		
		// Parse coverage from output
		const coverageMatch = testResult.output.match(/All files\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)\s+\|\s+([\d.]+)/)
		if (coverageMatch) {
			const [, statements, branches, functions, lines] = coverageMatch
			log('📈 Coverage Summary:', 'magenta')
			log(`  Statements: ${statements}%`, 'cyan')
			log(`  Branches: ${branches}%`, 'cyan')
			log(`  Functions: ${functions}%`, 'cyan')
			log(`  Lines: ${lines}%`, 'cyan')
			
			// Check if coverage meets threshold
			const threshold = 90
			const allAboveThreshold = [statements, branches, functions, lines]
				.every(coverage => parseFloat(coverage) >= threshold)
			
			if (allAboveThreshold) {
				log(`🎉 All coverage metrics above ${threshold}% threshold!`, 'green')
			} else {
				log(`⚠️  Some coverage metrics below ${threshold}% threshold`, 'yellow')
			}
		}
		
		process.exit(0)
	} else {
		log('❌ Tests failed:', 'red')
		log(testResult.output, 'red')
		process.exit(1)
	}
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
	log('💥 Uncaught exception:', 'red')
	log(error.stack || error.message, 'red')
	process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
	log('💥 Unhandled rejection:', 'red')
	log(reason, 'red')
	process.exit(1)
})

// Run the main function
main().catch((error) => {
	log('💥 Error running tests:', 'red')
	log(error.stack || error.message, 'red')
	process.exit(1)
})
