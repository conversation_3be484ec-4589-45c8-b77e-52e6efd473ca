"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_QAX_NEXT_EDIT_CONFIG = exports.QaxNextEditEventType = exports.QaxChangeType = void 0;
/**
 * 检测到的修改类型
 */
var QaxChangeType;
(function (QaxChangeType) {
    QaxChangeType["VARIABLE_RENAME"] = "variable_rename";
    QaxChangeType["FUNCTION_PARAMETER_CHANGE"] = "function_parameter_change";
    QaxChangeType["FUNCTION_CALL_DELETION"] = "function_call_deletion";
    QaxChangeType["VARIABLE_DELETION"] = "variable_deletion";
    QaxChangeType["IMPORT_CHANGE"] = "import_change";
    QaxChangeType["TYPE_CHANGE"] = "type_change";
})(QaxChangeType || (exports.QaxChangeType = QaxChangeType = {}));
/**
 * QaxNextEdit 事件类型
 */
var QaxNextEditEventType;
(function (QaxNextEditEventType) {
    QaxNextEditEventType["CHANGE_DETECTED"] = "change_detected";
    QaxNextEditEventType["SUGGESTIONS_GENERATED"] = "suggestions_generated";
    QaxNextEditEventType["SUGGESTION_APPLIED"] = "suggestion_applied";
    QaxNextEditEventType["SUGGESTION_IGNORED"] = "suggestion_ignored";
    QaxNextEditEventType["ANALYSIS_STARTED"] = "analysis_started";
    QaxNextEditEventType["ANALYSIS_COMPLETED"] = "analysis_completed";
    QaxNextEditEventType["ERROR_OCCURRED"] = "error_occurred";
})(QaxNextEditEventType || (exports.QaxNextEditEventType = QaxNextEditEventType = {}));
/**
 * 默认配置
 */
exports.DEFAULT_QAX_NEXT_EDIT_CONFIG = {
    enabled: true,
    enableLSPIntegration: true,
    enableASTAnalysis: true,
    debounceDelayMs: 1500,
    maxSuggestions: 8,
    confidenceThreshold: 0.7,
    supportedLanguages: [
        "typescript",
        "javascript",
        "python",
        "java",
        "csharp",
        "cpp",
        "c",
        "rust",
        "go",
        "php",
        "ruby",
        "swift",
        "kotlin"
    ],
    analysisDepth: "deep"
};
