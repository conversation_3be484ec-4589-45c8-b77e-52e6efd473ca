# Qax Codegen 开发规范文档

## 文档目标

本文档旨在建立 Qax Codegen 功能开发的标准化规范，确保：
1. **独立开发**：Qax 功能与 Cline 核心代码尽量完全隔离
2. **快速合并**：能够尽量无冲突地合并 Cline 最新版本
3. **功能隔离**：Qax 功能不影响 Cline 原生功能的稳定性
4. **代码质量**：保持与 Cline 一致的代码风格和架构模式

## 核心开发原则

### 1. 完全隔离原则
- **禁止修改 Cline 核心文件**：除非绝对必要的集成点
- **独立模块设计**：所有 Qax 功能尽可能在独立目录中实现
- **最小化侵入**：对 Cline 原生代码的修改必须控制在最小范围内

### 2. 代码风格一致性原则
- **遵循 Cline 编程模式**：函数设计、错误处理、状态管理等必须与 Cline 保持一致
- **复用现有组件**：优先使用 Cline 现有的 UI 组件和工具函数
- **统一命名规范**：严格遵循本文档定义的命名约定


## 文件命名规范

### 1. Qax 功能文件命名规则

#### 1.1 必须使用 Qax 前缀的文件类型
- **组件文件**：`Qax*.tsx` (如 `QaxAccountView.tsx`, `QaxWelcomeView.tsx`)
- **服务类文件**：`Qax*.ts` (如 `QaxAccountService.ts`)
- **控制器文件**：`qax*.ts` (如 `qaxLoginClicked.ts`, `qaxAuthStateChanged.ts`)
- **类型定义文件**：`Qax*.ts` (如 `QaxAccount.ts`)
- **配置文件**：`qax*.ts` (如 `qax.ts`, `qax-config.ts`)

#### 1.2 目录命名规范
- **Qax 独立模块**：`src/qax/`, `webview-ui/src/qax/`
- **Qax 相关文档**：`docs/qax/`, `docs/provider-config/qax.mdx`
- **Qax 资源文件**：`assets/icons/qax-*.png`

#### 1.3 存储键命名规范
遵循 Cline 的驼峰命名法（camelCase）规范：
- **认证相关**：`qaxToken`, `qaxUserInfo`, `qaxAuthState`
- **配置相关**：`qaxSettings`, `qaxModelConfig`, `qaxApiProvider`
- **状态相关**：`qaxWelcomeCompleted`, `qaxIsNewUser`
- **模型相关**：`qaxModelId`, `qaxModelInfo`, `qaxApiKey`

### 2. 变量和方法命名规范

#### 2.1 接口和类型命名
```typescript
// ✅ 正确示例
interface QaxUserInfo { ... }
interface QaxAuthState { ... }
type QaxModelConfig = { ... }
enum QaxProviderType { ... }

// ❌ 错误示例
interface UserInfo { ... }  // 缺少 Qax 前缀
interface AuthState { ... } // 可能与 Cline 冲突
```

#### 2.2 方法命名规范
```typescript
// ✅ 正确示例
qaxLoginClicked()
subscribeToQaxAuthCallback()
getQaxModels()
navigateToQaxAccount()

// ❌ 错误示例
loginClicked()     // 缺少 qax 前缀
getModels()        // 可能与 Cline 方法冲突
```

#### 2.3 变量命名规范
```typescript
// ✅ 正确示例
const qaxUserInfo = ...
const isQaxMode = ...
const qaxConfig = ...

// ❌ 错误示例
const userInfo = ...  // 缺少 qax 前缀
const config = ...    // 过于通用
```

## 代码风格规范

### 1. 与 Cline 保持一致的编程模式

#### 1.1 函数设计模式
```typescript
// ✅ 遵循 Cline 的 gRPC 控制器模式
export const qaxLoginClicked = async (
    call: ServerUnaryCall<QaxLoginClickedRequest, QaxLoginClickedResponse>,
    callback: sendUnaryData<QaxLoginClickedResponse>
) => {
    try {
        // 业务逻辑实现
        const result = await qaxAuthService.handleLogin()
        callback(null, { success: true, result })
    } catch (error) {
        callback(error as ServiceError, null)
    }
}

// ❌ 不符合 Cline 模式的实现
export function handleQaxLogin() {
    // 直接返回结果，不使用 gRPC 回调模式
    return qaxAuthService.login()
}
```

#### 1.2 错误处理模式
```typescript
// ✅ 遵循 Cline 的错误处理模式
try {
    const response = await apiCall()
    return response
} catch (error) {
    console.error(`Qax API call failed: ${error}`)
    throw new Error(`Qax operation failed: ${error instanceof Error ? error.message : String(error)}`)
}

// ❌ 不一致的错误处理
apiCall().catch(err => console.log(err)) // 过于简单的错误处理
```

#### 1.3 状态管理模式
```typescript
// ✅ 遵循 Cline 的状态管理模式
const [qaxUserInfo, setQaxUserInfo] = useState<QaxUserInfo | null>(null)

useEffect(() => {
    const subscription = qaxAuthService.subscribeToAuthState((state) => {
        setQaxUserInfo(state.userInfo)
    })
    return () => subscription.unsubscribe()
}, [])

// ❌ 不符合 Cline 模式
let qaxUserInfo = null // 使用全局变量而非 React 状态
```

### 2. 组件设计规范

#### 2.1 组件结构模式
```typescript
// ✅ 遵循 Cline 组件结构
interface QaxAccountViewProps {
    // 明确的 props 类型定义
}

const QaxAccountView: React.FC<QaxAccountViewProps> = ({ ...props }) => {
    // hooks 在顶部
    const [state, setState] = useState()

    // 事件处理函数
    const handleQaxLogin = useCallback(() => {
        // 实现逻辑
    }, [])

    // 渲染逻辑
    return (
        <div className="qax-account-view">
            {/* JSX 内容 */}
        </div>
    )
}

export default QaxAccountView
```

#### 2.2 CSS 类命名规范
```css
/* ✅ 使用 qax- 前缀避免样式冲突 */
.qax-account-view { }
.qax-login-button { }
.qax-user-info { }

/* ❌ 可能与 Cline 样式冲突 */
.account-view { }
.login-button { }
.user-info { }
```

#### 2.3 存储键使用规范
```typescript
// ✅ 遵循 Cline 驼峰命名规范
await updateGlobalState(context, 'qaxUserInfo', userInfo)
await updateGlobalState(context, 'qaxWelcomeCompleted', true)
await storeSecret(context, 'qaxToken', token)

// ❌ 错误的连字符命名
await updateGlobalState(context, 'qax-user-info', userInfo)  // 不符合 Cline 规范
await updateGlobalState(context, 'qax_user_info', userInfo) // 不符合 Cline 规范
```

## 修改限制规范

### 1. 禁止修改的 Cline 核心文件

#### 1.1 严格禁止修改的文件
```
❌ 绝对禁止修改：
- package.json                    # 依赖管理（当前违规）
- webview-ui/package.json        # 前端依赖
- CHANGELOG.md                   # 变更日志
- README.md                      # 项目说明
- LICENSE                        # 许可证文件
```

#### 1.2 核心业务逻辑文件（禁止修改）
```
❌ 禁止修改的核心文件：
- src/core/task/index.ts         # 任务核心逻辑（当前违规）
- src/core/controller/task/askResponse.ts  # 响应处理（当前违规）
- src/core/controller/task/newTask.ts      # 新任务创建（当前违规）
- src/core/webview/index.ts      # Webview 核心（当前违规）
```

### 2. 允许修改的集成点文件

#### 2.1 必要的集成点（最小化修改）
```
✅ 允许最小化修改：
- src/extension.ts               # 扩展入口（仅添加 Qax 命令注册）
- src/core/controller/index.ts   # 控制器注册（仅添加 Qax 控制器）
- src/shared/api.ts             # API 类型（仅添加 Qax Provider 类型）
- webview-ui/src/main.tsx       # 应用入口（仅添加条件选择逻辑）
```

#### 2.2 UI 组件集成点（谨慎修改, 尽量独立出去）
```
⚠️ 谨慎修改（仅添加 Qax 相关逻辑）：
- webview-ui/src/components/account/AccountView.tsx
- webview-ui/src/components/settings/ApiOptions.tsx
- webview-ui/src/components/welcome/WelcomeView.tsx
- webview-ui/src/context/ExtensionStateContext.tsx
```

### 3. 修改原则和检查清单

#### 3.1 修改前检查清单
- [ ] 是否可以通过独立模块实现？
- [ ] 是否可以通过扩展现有接口实现？
- [ ] 修改是否仅限于添加 Qax 相关代码？
- [ ] 修改是否使用条件判断隔离 Qax 逻辑？
- [ ] 是否保持了 Cline 原有功能的完整性？

#### 3.2 修改实现模式
```typescript
// ✅ 正确的修改模式：条件隔离
if (isQaxMode) {
    // Qax 特有逻辑
    return handleQaxSpecificLogic()
} else {
    // 保持 Cline 原有逻辑不变
    return originalClineLogic()
}

// ❌ 错误的修改模式：直接替换
// 直接修改 Cline 原有逻辑，可能破坏原有功能
return modifiedLogicForBothClineAndQax()
```

## 当前问题识别与整改方案

### 1. 违规文件修改问题

#### 1.1 严重违规：修改核心依赖文件
```
🚨 问题文件：
- package.json                   # 添加了自动补全相关依赖

💡 整改方案：
1. 将自动补全相关依赖移至独立的 qax/package.json
2. 在构建时合并依赖，而非直接修改原文件
3. 使用 npm workspace 或类似机制管理 Qax 独立依赖
```

#### 1.2 违规：修改核心业务逻辑
```
🚨 问题文件：
- src/core/task/index.ts         # 添加了记忆相关逻辑
- src/core/controller/task/askResponse.ts  # 修改了响应处理
- src/core/controller/task/newTask.ts      # 修改了任务创建逻辑

💡 整改方案：
1. 将记忆功能完全移至 src/qax/services/memory/
2. 通过事件机制或插件模式集成，而非直接修改核心逻辑
3. 使用装饰器模式或中间件模式扩展功能
```

### 2. 命名规范违规问题

#### 2.1 缺少 Qax 前缀的文件
```
🚨 问题文件：
- src/shared/AutocompleteSettings.ts     # 应为 QaxAutocompleteSettings.ts
- src/services/autocomplete/*            # 应移至 src/qax/services/
- webview-ui/src/components/chat/WorkspaceHeader.tsx  # 非 Qax 专用但被修改

💡 整改方案：
1. 重命名所有缺少前缀的文件
2. 将通用功能移至 Qax 独立目录
3. 恢复被不当修改的 Cline 原生文件
```

#### 2.2 存储键命名不规范
```
🚨 问题：
- 部分存储键使用了连字符命名，不符合 Cline 驼峰命名规范
- 缺少统一的 qax 前缀（驼峰形式）

💡 整改方案：
1. 将所有存储键改为驼峰命名：qax-token → qaxToken
2. 确保所有存储键使用 qax 前缀（驼峰形式）
3. 建立存储键注册表，避免与 Cline 原生键冲突
4. 实现存储键迁移机制处理历史数据
```

### 3. 架构违规问题

#### 3.1 过度侵入 Cline 核心
```
🚨 问题：
- 直接修改 Cline 核心控制器
- 在 Cline 组件中添加 Qax 特定逻辑
- 修改 Cline 的状态管理逻辑

💡 整改方案：
1. 实现 Qax 功能的完全独立化
2. 使用事件驱动架构减少耦合
3. 通过配置和插件机制实现功能扩展
```

### 4. 整改优先级和时间计划

#### 4.1 高优先级（立即整改）
1. **恢复 package.json 文件**：移除所有 Qax 相关依赖修改
2. **隔离核心逻辑修改**：将记忆功能等移至独立模块
3. **修复命名规范**：重命名所有违规文件

#### 4.2 中优先级（1周内完成）
1. **重构集成点**：使用更优雅的集成方式
2. **完善独立模块**：确保 Qax 功能完全独立
3. **建立检查机制**：防止未来违规修改

#### 4.3 低优先级（持续改进）
1. **优化架构设计**：进一步减少耦合
2. **完善文档**：更新开发规范
3. **建立自动化检查**：CI/CD 集成规范检查

## Git 工作流规范

QAX Codegen 项目采用严格的 Git 分支管理策略，确保 Cline 版本升级和 QAX 功能开发的有序进行。

### 分支管理策略
```
upstream/tags → master → codegen → feature/*
```

- **master**: 同步 Cline 官方稳定版本，保持纯净
- **codegen**: 集成分支，包含 Cline + QAX 功能
- **feature/***: 基于 codegen 的功能开发分支

### 核心工作流程
1. **Cline 版本升级**: 使用 squash merge 将新版本合并到 master，然后 rebase 到 codegen
2. **功能开发**: 基于 codegen 创建 feature 分支，开发完成后合并回 codegen
3. **冲突处理**: QAX 功能优先，Cline 升级优先，复杂情况手动合并

### 提交信息规范
```
feat(qax): 添加 QAX 新功能
fix(qax): 修复 QAX 相关问题
upgrade: Cline 版本升级到 vX.X.X
```

### 详细操作指南
完整的工作流程、冲突处理策略和具体操作步骤请参考：
- `docs/qax/git-workflow/workflow.md` - 详细工作流程文档
- `docs/qax/git-workflow/README.md` - 合并记录管理
- `docs/qax/git-workflow/v*.md` - 具体版本升级记录

## 质量控制检查清单

### 1. 代码提交前检查

#### 1.1 文件命名检查
- [ ] 所有 Qax 相关文件使用正确前缀
- [ ] 新增文件位于正确的目录结构中
- [ ] 没有修改禁止修改的 Cline 核心文件

#### 1.2 代码风格检查
- [ ] 遵循 Cline 的编程模式和风格
- [ ] 使用正确的错误处理模式
- [ ] 状态管理符合 Cline 规范
- [ ] CSS 类名使用 qax- 前缀

#### 1.3 架构合规检查
- [ ] Qax 功能完全独立，不影响 Cline 核心
- [ ] 使用条件判断隔离 Qax 逻辑
- [ ] 没有在 Cline 核心逻辑中硬编码 Qax 功能
- [ ] 存储键使用 qax- 前缀

### 2. 版本升级检查

#### 2.1 升级前准备
- [ ] 创建当前状态的备份分支
- [ ] 确认所有功能分支已合并或有明确处理计划
- [ ] 准备升级文档模板

#### 2.2 升级后验证
- [ ] Qax 认证功能正常
- [ ] Qax 模型调用正常
- [ ] Qax UI 组件显示正确
- [ ] Cline 原生功能未受影响
- [ ] 构建和打包流程正常

### 3. 发布前检查

#### 3.1 功能完整性
- [ ] 所有 Qax 功能按预期工作
- [ ] 错误处理和边界情况处理完善
- [ ] 用户体验流畅，无明显问题

#### 3.2 文档完整性
- [ ] 更新了相关的开发文档
- [ ] 创建了版本升级记录（如适用）
- [ ] API 文档与实现保持同步

## 总结

本开发规范文档建立了 Qax Codegen 功能开发的标准化流程，确保：

1. **独立开发**：通过严格的文件命名和目录结构规范，实现 Qax 功能与 Cline 的完全隔离
2. **快速合并**：通过明确的 Git 工作流和冲突处理策略，确保能够无障碍地合并 Cline 最新版本
3. **功能隔离**：通过修改限制和架构规范，确保 Qax 功能不影响 Cline 原生功能的稳定性
4. **代码质量**：通过代码风格规范和质量检查清单，保持与 Cline 一致的代码质量标准

遵循本规范，可以有效避免开发过程中的常见问题，提高代码质量，确保项目的长期可维护性。
