# 统一内存处理测试套件

这是一个全面的测试套件，用于验证统一内存处理功能的正确性和完整性。

## 测试结构

```
test/
├── UnifiedMemoryTest.ts     # 主要单元测试
├── IntegrationTest.ts       # 集成测试
├── TestCoverage.ts          # 测试覆盖率分析
├── runTests.ts              # 测试运行器
├── package.json             # 测试脚本配置
└── README.md                # 本文档
```

## 测试覆盖范围

### ✅ 核心功能测试

1. **操作类型覆盖**
   - ✅ ADD - 添加新记忆条目
   - ✅ UPDATE - 更新现有条目
   - ✅ REMOVE - 删除过时条目
   - ✅ MERGE - 合并相似条目

2. **记忆分类覆盖**
   - ✅ TOOL_PREFERENCES - 工具偏好
   - ✅ TECH_STACK - 技术栈
   - ✅ CODE_STYLE - 代码风格
   - ✅ WORKFLOW - 工作流程
   - ✅ GENERAL - 一般信息

3. **边界情况测试**
   - ✅ 空输入处理
   - ✅ 超长输入处理
   - ✅ 特殊字符处理
   - ✅ 最大条目数限制
   - ✅ 并发处理

4. **错误处理测试**
   - ✅ API 超时处理
   - ✅ 无效 JSON 响应
   - ✅ 无效操作类型
   - ✅ 网络错误处理
   - ✅ 文件写入错误

5. **配置选项测试**
   - ✅ useUnifiedProcessing 开关
   - ✅ enabled 开关
   - ✅ minInputLength 设置
   - ✅ maxEntriesPerCategory 限制
   - ✅ confidenceThreshold 阈值
   - ✅ debounceMs 防抖设置

### ✅ 集成测试

1. **MemoryService 集成**
   - ✅ 服务初始化
   - ✅ 配置加载
   - ✅ 文件持久化
   - ✅ 错误恢复

2. **VS Code 集成**
   - ✅ 扩展上下文模拟
   - ✅ 工作区配置模拟
   - ✅ API 处理器集成

## 运行测试

### 前置条件

```bash
# 安装依赖
npm install ts-node typescript
```

### 运行所有测试

```bash
# 运行完整测试套件
npm run test:all

# 或者直接运行
node -r ts-node/register runTests.ts
```

### 运行特定测试

```bash
# 只运行单元测试
npm run test:unit

# 只运行集成测试
npm run test:integration

# 只生成覆盖率报告
npm run test:coverage

# 调试模式运行
npm run test:debug
```

### 手动运行

```typescript
import { runAllTests } from './UnifiedMemoryTest'

// 运行所有测试
await runAllTests()
```

## 测试输出示例

```
🚀 Running all unified memory processing tests...

📋 Running normal operation tests...
🧪 Running test: ADD - New tool preference
✅ Test passed: ADD - New tool preference
🧪 Running test: UPDATE - Modify existing preference
✅ Test passed: UPDATE - Modify existing preference
...

⚠️  Running error handling tests...
🧪 Running test: ERROR - API timeout handling
✅ API timeout handled gracefully
...

🔍 Running edge case tests...
🧪 Running test: EDGE - Empty input
✅ Test passed: EDGE - Empty input
...

⚡ Running performance tests...
⚡ PERF - Single operation: 150ms total, 150.0ms average
⚡ PERF - Multiple operations: 750ms total, 150.0ms average

🔗 Running integration tests...
🧪 Testing MemoryService integration...
✅ MemoryService integration test passed
...

📊 Generating test coverage report...
📊 Test Coverage Report
==================================================

🔧 Operations Coverage: 100.0%
✅ Covered: ADD, UPDATE, REMOVE, MERGE

📂 Categories Coverage: 100.0%
✅ Covered: tool_preferences, tech_stack, code_style, workflow, general

⚠️  Error Cases Coverage: 100.0%
✅ Covered: api_timeout, invalid_json, invalid_operation, network_error, file_write_error

🔍 Edge Cases Coverage: 100.0%
✅ Covered: empty_input, very_long_input, special_characters, max_entries_limit, concurrent_processing

⚙️  Config Options Coverage: 100.0%
✅ Covered: useUnifiedProcessing, enabled, minInputLength, maxEntriesPerCategory, confidenceThreshold, debounceMs

🎯 Overall Coverage: 100.0%
🎉 Excellent coverage!
==================================================

📊 Test Results:
✅ Passed: 25
❌ Failed: 0
📈 Total: 25
🎯 Success Rate: 100.0%

🎉 All test suites completed successfully!
```

## 测试用例说明

### 单元测试用例

1. **ADD 操作测试**
   - 验证新记忆条目的正确添加
   - 检查分类和置信度设置
   - 确认文件持久化

2. **UPDATE 操作测试**
   - 验证现有条目的正确更新
   - 保持原有 ID 和时间戳
   - 更新摘要和置信度

3. **REMOVE 操作测试**
   - 验证条目的正确删除
   - 确认目标条目不再存在
   - 检查文件更新

4. **MERGE 操作测试**
   - 验证多个条目的合并
   - 创建新的合并条目
   - 删除原有条目

### 集成测试用例

1. **服务初始化测试**
   - 验证 MemoryService 正确初始化
   - 检查配置加载
   - 确认 API 处理器连接

2. **端到端处理测试**
   - 从用户输入到文件保存的完整流程
   - 验证所有中间步骤
   - 检查最终结果

## 添加新测试

### 添加单元测试

```typescript
// 在 testCases 数组中添加新测试用例
{
  name: "TEST - Your test name",
  input: "测试输入",
  expectedResponse: {
    shouldUpdate: true,
    instructions: [
      {
        operation: MemoryUpdateOperation.ADD,
        category: MemoryCategory.GENERAL,
        newEntry: {
          summary: "期望的摘要",
          confidence: 0.8,
          reasoning: "测试理由"
        }
      }
    ],
    reasoning: "测试推理"
  }
}
```

### 添加错误测试

```typescript
// 在 errorTestCases 数组中添加错误测试用例
{
  name: "ERROR - Your error test",
  input: "错误测试输入",
  expectedResponse: {
    shouldUpdate: false,
    instructions: [],
    reasoning: "错误处理"
  },
  expectedError: "error_type"
}
```

## 持续集成

这个测试套件可以集成到 CI/CD 流水线中：

```yaml
# .github/workflows/test.yml
- name: Run Memory Tests
  run: |
    cd src/core/services/memory/test
    npm install
    npm run test:all
```

## 性能基准

- 单次操作应在 2 秒内完成
- 批量操作平均每个操作不超过 500ms
- 内存使用应保持在合理范围内
- 文件 I/O 操作应有适当的错误处理

## 故障排除

### 常见问题

1. **测试超时**
   - 检查 API 模拟器配置
   - 增加超时时间设置
   - 验证网络连接

2. **文件权限错误**
   - 确保测试目录可写
   - 检查临时文件清理
   - 验证路径设置

3. **类型错误**
   - 更新 TypeScript 版本
   - 检查类型定义
   - 验证导入路径

### 调试技巧

```bash
# 启用详细日志
DEBUG=1 npm run test:all

# 运行单个测试
node -r ts-node/register -e "
import('./UnifiedMemoryTest').then(m => 
  m.testCases[0] && console.log('First test:', m.testCases[0])
)"
```
