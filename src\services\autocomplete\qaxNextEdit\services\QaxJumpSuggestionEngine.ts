import * as vscode from "vscode"
import * as path from "path"
import { 
	QaxChangeDetection, 
	QaxJumpSuggestion, 
	QaxChangeType, 
	QaxAnalysisContext,
	QaxNextEditConfig 
} from "../types/QaxNextEditTypes"
import { QaxLSPService } from "./QaxLSPService"

/**
 * 跳转和建议引擎，提供精确的跳转位置和修改建议
 */
export class QaxJumpSuggestionEngine {
	private lspService: QaxLSPService
	private config: QaxNextEditConfig

	constructor(config: QaxNextEditConfig) {
		this.config = config
		this.lspService = QaxLSPService.getInstance()
	}

	/**
	 * 生成跳转建议
	 */
	async generateJumpSuggestions(
		changes: QaxChangeDetection[], 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		for (const change of changes) {
			try {
				switch (change.type) {
					case QaxChangeType.VARIABLE_RENAME:
						const renameSuggestions = await this.generateVariableRenameSuggestions(change, context)
						suggestions.push(...renameSuggestions)
						break

					case QaxChangeType.FUNCTION_PARAMETER_CHANGE:
						const paramSuggestions = await this.generateParameterChangeSuggestions(change, context)
						suggestions.push(...paramSuggestions)
						break

					case QaxChangeType.FUNCTION_CALL_DELETION:
						const deletionSuggestions = await this.generateCallDeletionSuggestions(change, context)
						suggestions.push(...deletionSuggestions)
						break

					case QaxChangeType.VARIABLE_DELETION:
						const varDeletionSuggestions = await this.generateVariableDeletionSuggestions(change, context)
						suggestions.push(...varDeletionSuggestions)
						break

					case QaxChangeType.IMPORT_CHANGE:
						const importSuggestions = await this.generateImportChangeSuggestions(change, context)
						suggestions.push(...importSuggestions)
						break

					case QaxChangeType.TYPE_CHANGE:
						const typeSuggestions = await this.generateTypeChangeSuggestions(change, context)
						suggestions.push(...typeSuggestions)
						break
				}
			} catch (error) {
				console.warn(`QaxJumpSuggestionEngine: Failed to generate suggestions for ${change.type}:`, error)
			}
		}

		// 排序和限制数量
		const sortedSuggestions = suggestions
			.sort((a, b) => b.priority - a.priority)
			.slice(0, this.config.maxSuggestions)

		return sortedSuggestions
	}

	/**
	 * 生成变量重命名建议
	 */
	private async generateVariableRenameSuggestions(
		change: QaxChangeDetection, 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.newValue || !change.metadata?.symbolName) {
			return suggestions
		}

		// 获取所有引用位置
		const references = change.metadata.references as vscode.Location[] || []
		
		// 如果没有 LSP 引用信息，尝试通过 LSP 获取
		if (references.length === 0) {
			try {
				const position = change.range.start
				const lspReferences = await this.lspService.getReferences(context.document, position)
				references.push(...lspReferences)
			} catch (error) {
				console.warn("Failed to get LSP references:", error)
			}
		}

		// 为每个引用位置生成建议
		for (const reference of references) {
			// 跳过当前修改位置
			if (reference.uri.fsPath === context.filePath && 
				reference.range.isEqual(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `rename-${change.metadata.symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Update variable name from '${change.oldValue}' to '${change.newValue}'`,
				changeType: QaxChangeType.VARIABLE_RENAME,
				suggestedEdit: {
					range: reference.range,
					newText: change.newValue,
					description: `Rename '${change.oldValue}' to '${change.newValue}'`
				},
				priority: this.calculatePriority(reference, context, change),
				relatedChange: change
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成函数参数变更建议
	 */
	private async generateParameterChangeSuggestions(
		change: QaxChangeDetection, 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		const functionName = change.metadata.symbolName
		
		// 查找函数调用位置
		const callSites = await this.findFunctionCallSites(functionName, context)

		for (const callSite of callSites) {
			const suggestion: QaxJumpSuggestion = {
				id: `param-change-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
				filePath: callSite.uri.fsPath,
				range: callSite.range,
				description: `Update function call '${functionName}' to match new parameters`,
				changeType: QaxChangeType.FUNCTION_PARAMETER_CHANGE,
				suggestedEdit: {
					range: callSite.range,
					newText: this.generateUpdatedFunctionCall(callSite, change),
					description: `Update parameters for '${functionName}'`
				},
				priority: this.calculatePriority(callSite, context, change),
				relatedChange: change
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成函数调用删除建议
	 */
	private async generateCallDeletionSuggestions(
		change: QaxChangeDetection, 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		const functionName = change.metadata.symbolName
		
		// 查找其他相关的函数调用
		const relatedCalls = await this.findFunctionCallSites(functionName, context)

		for (const callSite of relatedCalls) {
			// 跳过已删除的调用
			if (callSite.uri.fsPath === context.filePath && 
				callSite.range.intersection(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `call-deletion-${functionName}-${callSite.uri.fsPath}-${callSite.range.start.line}`,
				filePath: callSite.uri.fsPath,
				range: callSite.range,
				description: `Consider removing this call to '${functionName}' as well`,
				changeType: QaxChangeType.FUNCTION_CALL_DELETION,
				suggestedEdit: {
					range: callSite.range,
					newText: "",
					description: `Remove call to '${functionName}'`
				},
				priority: this.calculatePriority(callSite, context, change) - 2, // 降低优先级
				relatedChange: change
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成变量删除建议
	 */
	private async generateVariableDeletionSuggestions(
		change: QaxChangeDetection, 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		const suggestions: QaxJumpSuggestion[] = []

		if (!change.metadata?.symbolName) {
			return suggestions
		}

		// 查找变量的所有使用位置
		const references = change.metadata.references as vscode.Location[] || []

		for (const reference of references) {
			// 跳过删除位置
			if (reference.uri.fsPath === context.filePath && 
				reference.range.intersection(change.range)) {
				continue
			}

			const suggestion: QaxJumpSuggestion = {
				id: `var-deletion-${change.metadata.symbolName}-${reference.uri.fsPath}-${reference.range.start.line}`,
				filePath: reference.uri.fsPath,
				range: reference.range,
				description: `Remove usage of deleted variable '${change.metadata.symbolName}'`,
				changeType: QaxChangeType.VARIABLE_DELETION,
				suggestedEdit: {
					range: reference.range,
					newText: "",
					description: `Remove reference to '${change.metadata.symbolName}'`
				},
				priority: this.calculatePriority(reference, context, change),
				relatedChange: change
			}

			suggestions.push(suggestion)
		}

		return suggestions
	}

	/**
	 * 生成导入变更建议
	 */
	private async generateImportChangeSuggestions(
		change: QaxChangeDetection, 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		// 暂时返回空数组，可以后续实现
		return []
	}

	/**
	 * 生成类型变更建议
	 */
	private async generateTypeChangeSuggestions(
		change: QaxChangeDetection, 
		context: QaxAnalysisContext
	): Promise<QaxJumpSuggestion[]> {
		// 暂时返回空数组，可以后续实现
		return []
	}

	/**
	 * 查找函数调用位置
	 */
	private async findFunctionCallSites(functionName: string, context: QaxAnalysisContext): Promise<vscode.Location[]> {
		const callSites: vscode.Location[] = []

		try {
			// 简化实现：只在当前文档中查找函数调用
			const document = context.document
			const content = document.getText()
			const lines = content.split('\n')

			const callPattern = new RegExp(`\\b${functionName}\\s*\\(`, 'g')

			for (let lineIndex = 0; lineIndex < lines.length; lineIndex++) {
				const line = lines[lineIndex]
				let match

				while ((match = callPattern.exec(line)) !== null) {
					const startPos = new vscode.Position(lineIndex, match.index)
					const endPos = new vscode.Position(lineIndex, match.index + match[0].length)
					const range = new vscode.Range(startPos, endPos)
					const location = new vscode.Location(document.uri, range)
					callSites.push(location)
				}
			}
		} catch (error) {
			console.warn("Failed to find function call sites:", error)
		}

		return callSites
	}

	/**
	 * 生成更新后的函数调用
	 */
	private generateUpdatedFunctionCall(callSite: vscode.Location, change: QaxChangeDetection): string {
		// 这里需要根据具体的参数变更生成新的函数调用
		// 暂时返回原始文本，实际实现需要解析函数调用并更新参数
		return "/* TODO: Update function call parameters */"
	}

	/**
	 * 计算建议的优先级
	 */
	private calculatePriority(location: vscode.Location, context: QaxAnalysisContext, change: QaxChangeDetection): number {
		let priority = 5 // 基础优先级

		// 同一文件的建议优先级更高
		if (location.uri.fsPath === context.filePath) {
			priority += 3
		}

		// 距离变更位置越近，优先级越高
		if (location.uri.fsPath === context.filePath) {
			const lineDiff = Math.abs(location.range.start.line - change.range.start.line)
			if (lineDiff <= 10) {
				priority += 2
			} else if (lineDiff <= 50) {
				priority += 1
			}
		}

		// 根据置信度调整优先级
		priority += Math.floor(change.confidence * 3)

		// 根据文件类型调整优先级
		const ext = path.extname(location.uri.fsPath).toLowerCase()
		if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
			priority += 1 // TypeScript/JavaScript 文件优先级稍高
		}

		return Math.max(1, Math.min(10, priority)) // 限制在 1-10 范围内
	}

	/**
	 * 更新配置
	 */
	updateConfig(config: QaxNextEditConfig): void {
		this.config = config
	}
}
