"use strict";
/**
 * Integration test for QaxNextEdit
 * Tests the complete functionality with mocked VS Code API
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
// Mock VS Code API first
const mockVscode = {
    workspace: {
        onDidChangeTextDocument: () => ({ dispose: () => { } }),
        onDidOpenTextDocument: () => ({ dispose: () => { } }),
        onDidCloseTextDocument: () => ({ dispose: () => { } }),
        onDidChangeConfiguration: () => ({ dispose: () => { } }),
        getConfiguration: () => ({
            get: (key, defaultValue) => defaultValue,
            update: () => Promise.resolve()
        }),
        openTextDocument: async (options) => ({
            uri: { fsPath: options.content ? "mock.ts" : "test.ts" },
            languageId: options.language || "typescript",
            getText: () => options.content || "mock content",
            offsetAt: (position) => position.line * 100 + position.character
        }),
        findTextInFiles: async () => new Map()
    },
    window: {
        onDidChangeActiveTextEditor: () => ({ dispose: () => { } }),
        createStatusBarItem: () => ({
            text: "",
            show: () => { },
            hide: () => { },
            dispose: () => { }
        }),
        createTextEditorDecorationType: () => ({
            dispose: () => { }
        }),
        showTextDocument: async (document) => ({
            document,
            setDecorations: () => { },
            selection: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
            revealRange: () => { }
        }),
        showInformationMessage: () => Promise.resolve(),
        showWarningMessage: () => Promise.resolve(),
        showErrorMessage: () => Promise.resolve(),
        showQuickPick: () => Promise.resolve()
    },
    languages: {
        onDidChangeDiagnostics: () => ({ dispose: () => { } }),
        registerHoverProvider: () => ({ dispose: () => { } })
    },
    commands: {
        executeCommand: async () => null,
        registerCommand: () => ({ dispose: () => { } })
    },
    StatusBarAlignment: { Right: 2 },
    TextEditorRevealType: { InCenter: 1 },
    SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
    ConfigurationTarget: { Global: 1 },
    ThemeColor: class {
        constructor(id) {
            this.id = id;
        }
    },
    MarkdownString: class {
        constructor(value = "") {
            this.value = value;
            this.isTrusted = false;
            this.isTrusted = false;
        }
        appendMarkdown(value) { this.value += value; return this; }
        appendCodeblock(value, language) {
            this.value += `\n\`\`\`${language || ''}\n${value}\n\`\`\`\n`;
            return this;
        }
    },
    Hover: class {
        constructor(contents, range) {
            this.contents = contents;
            this.range = range;
        }
    },
    Uri: {
        file: (path) => ({ fsPath: path, scheme: "file" })
    },
    Range: class {
        constructor(start, end) {
            this.start = start;
            this.end = end;
        }
        get isEmpty() {
            return this.start.line === this.end.line && this.start.character === this.end.character;
        }
        isEqual(other) {
            return this.start.line === other.start.line &&
                this.start.character === other.start.character &&
                this.end.line === other.end.line &&
                this.end.character === other.end.character;
        }
        intersection(other) {
            const startLine = Math.max(this.start.line, other.start.line);
            const endLine = Math.min(this.end.line, other.end.line);
            if (startLine > endLine)
                return null;
            return new this.constructor({ line: startLine, character: 0 }, { line: endLine, character: 0 });
        }
    },
    Position: class {
        constructor(line, character) {
            this.line = line;
            this.character = character;
        }
        isEqual(other) {
            return this.line === other.line && this.character === other.character;
        }
    },
    Selection: class {
        constructor(start, end) {
            this.start = start;
            this.end = end;
        }
    },
    Location: class {
        constructor(uri, range) {
            this.uri = uri;
            this.range = range;
        }
    },
    CancellationTokenSource: class {
        constructor() {
            this.token = { isCancellationRequested: false };
        }
        cancel() { this.token.isCancellationRequested = true; }
        dispose() { }
    },
    CancellationError: class extends Error {
    }
};
// Apply mocks to global vscode
const vscode = mockVscode;
global.vscode = vscode;
// Mock createDebouncedFn
const createDebouncedFn = (fn, delay) => {
    return async (...args) => {
        // For testing, just call the function directly without debouncing
        try {
            return await fn(...args);
        }
        catch (error) {
            return null;
        }
    };
};
// Mock getParserForFile
const getParserForFile = async (filePath) => {
    if (filePath.endsWith('.ts') || filePath.endsWith('.js')) {
        return {
            parse: (content) => ({
                rootNode: {
                    type: "program",
                    text: content,
                    startPosition: { row: 0, column: 0 },
                    endPosition: { row: content.split('\n').length - 1, column: content.length },
                    childCount: 0,
                    namedChildCount: 0,
                    child: () => null
                }
            })
        };
    }
    return null;
};
// Import and test services
const QaxNextEditTypes_1 = require("../types/QaxNextEditTypes");
// Test counter
let testCount = 0;
let passedCount = 0;
let failedCount = 0;
function test(name, fn) {
    testCount++;
    console.log(`\n🧪 Test ${testCount}: ${name}`);
    try {
        const result = fn();
        if (result instanceof Promise) {
            return result.then(() => {
                console.log(`✅ PASSED: ${name}`);
                passedCount++;
            }).catch((error) => {
                console.log(`❌ FAILED: ${name}`);
                console.log(`   Error: ${error.message}`);
                failedCount++;
            });
        }
        else {
            console.log(`✅ PASSED: ${name}`);
            passedCount++;
        }
    }
    catch (error) {
        console.log(`❌ FAILED: ${name}`);
        console.log(`   Error: ${error.message}`);
        failedCount++;
    }
}
async function runIntegrationTests() {
    console.log("🚀 Starting QaxNextEdit Integration Tests");
    console.log("=".repeat(50));
    // Test 1: Service initialization
    test("Service should initialize correctly", async () => {
        var _a;
        // Mock the required modules
        const mockQaxNextEditService = (_a = class {
                constructor(config) {
                    this.disposables = [];
                    this.config = config;
                    this.state = {
                        isEnabled: config.enabled,
                        isAnalyzing: false,
                        pendingChanges: new Map(),
                        cachedResults: new Map()
                    };
                }
                static getInstance(config) {
                    if (!this.instance) {
                        this.instance = new this(config || QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG);
                    }
                    return this.instance;
                }
                static dispose() {
                    if (this.instance) {
                        this.instance.dispose();
                        this.instance = null;
                    }
                }
                getConfig() { return this.config; }
                getState() { return this.state; }
                setEnabled(enabled) { this.state.isEnabled = enabled; }
                updateConfig(updates) { this.config = { ...this.config, ...updates }; }
                getAnalysisResult() { return null; }
                getJumpSuggestions() { return []; }
                dispose() { this.disposables.forEach(d => d.dispose?.()); }
            },
            __setFunctionName(_a, "mockQaxNextEditService"),
            _a.instance = null,
            _a);
        const service = mockQaxNextEditService.getInstance();
        assert.ok(service);
        assert.strictEqual(service.getState().isEnabled, true);
        service.dispose();
    });
    // Test 2: Configuration management
    test("Configuration should be manageable", () => {
        const config = { ...QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG };
        // Test default values
        assert.strictEqual(config.enabled, true);
        assert.strictEqual(config.debounceDelayMs, 1500);
        assert.strictEqual(config.maxSuggestions, 8);
        // Test configuration updates
        config.debounceDelayMs = 2000;
        config.maxSuggestions = 10;
        assert.strictEqual(config.debounceDelayMs, 2000);
        assert.strictEqual(config.maxSuggestions, 10);
    });
    // Test 3: Change type detection
    test("Change types should be properly categorized", () => {
        const changeTypes = [
            QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
            QaxNextEditTypes_1.QaxChangeType.FUNCTION_PARAMETER_CHANGE,
            QaxNextEditTypes_1.QaxChangeType.FUNCTION_CALL_DELETION,
            QaxNextEditTypes_1.QaxChangeType.VARIABLE_DELETION,
            QaxNextEditTypes_1.QaxChangeType.IMPORT_CHANGE,
            QaxNextEditTypes_1.QaxChangeType.TYPE_CHANGE
        ];
        assert.strictEqual(changeTypes.length, 6);
        assert.ok(changeTypes.every(type => typeof type === 'string'));
    });
    // Test 4: Event system
    test("Event system should work correctly", () => {
        const events = [];
        const mockEventCallback = (event) => {
            events.push(event);
        };
        // Simulate event emission
        const mockEvent = {
            type: QaxNextEditTypes_1.QaxNextEditEventType.CHANGE_DETECTED,
            timestamp: new Date(),
            filePath: "test.ts",
            data: { changesCount: 1 }
        };
        mockEventCallback(mockEvent);
        assert.strictEqual(events.length, 1);
        assert.strictEqual(events[0].type, QaxNextEditTypes_1.QaxNextEditEventType.CHANGE_DETECTED);
        assert.strictEqual(events[0].filePath, "test.ts");
    });
    // Test 5: Mock VS Code API integration
    test("VS Code API mocks should work", async () => {
        // Test workspace API
        const config = vscode.workspace.getConfiguration();
        assert.ok(config);
        assert.ok(typeof config.get === 'function');
        // Test window API
        const statusBarItem = vscode.window.createStatusBarItem();
        assert.ok(statusBarItem);
        assert.ok(typeof statusBarItem.show === 'function');
        // Test document creation
        const document = await vscode.workspace.openTextDocument({
            content: "let x = 5;",
            language: "typescript"
        });
        assert.ok(document);
        assert.strictEqual(document.languageId, "typescript");
        assert.strictEqual(document.getText(), "let x = 5;");
    });
    // Test 6: Range and Position utilities
    test("Range and Position utilities should work", () => {
        const position1 = new vscode.Position(0, 5);
        const position2 = new vscode.Position(0, 5);
        const position3 = new vscode.Position(1, 0);
        assert.ok(position1.isEqual(position2));
        assert.ok(!position1.isEqual(position3));
        const range1 = new vscode.Range(position1, position3);
        const range2 = new vscode.Range(position1, position3);
        assert.ok(range1.isEqual(range2));
        assert.ok(!range1.isEmpty);
        const emptyRange = new vscode.Range(position1, position1);
        assert.ok(emptyRange.isEmpty);
    });
    // Test 7: Debounced function mock
    test("Debounced function should work", async () => {
        let callCount = 0;
        const testFn = async (value) => {
            callCount++;
            return value * 2;
        };
        const debouncedFn = createDebouncedFn(testFn, 100);
        const result = await debouncedFn(5);
        assert.strictEqual(result, 10);
        assert.strictEqual(callCount, 1);
    });
    // Test 8: Parser mock
    test("Parser mock should work", async () => {
        const parser = await getParserForFile("test.ts");
        assert.ok(parser);
        const tree = parser.parse("let x = 5;");
        assert.ok(tree.rootNode);
        assert.strictEqual(tree.rootNode.type, "program");
        assert.strictEqual(tree.rootNode.text, "let x = 5;");
        // Test unsupported file
        const noParser = await getParserForFile("test.txt");
        assert.strictEqual(noParser, null);
    });
    // Test 9: Language support validation
    test("Language support should be comprehensive", () => {
        const supportedLanguages = QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG.supportedLanguages;
        // Test major languages
        const majorLanguages = ["typescript", "javascript", "python", "java", "csharp"];
        for (const lang of majorLanguages) {
            assert.ok(supportedLanguages.includes(lang), `Should support ${lang}`);
        }
        // Test total count
        assert.ok(supportedLanguages.length >= 10, "Should support at least 10 languages");
    });
    // Test 10: Error handling
    test("Error handling should be robust", async () => {
        // Test with invalid input
        const invalidConfig = { ...QaxNextEditTypes_1.DEFAULT_QAX_NEXT_EDIT_CONFIG };
        invalidConfig.confidenceThreshold = -1; // Invalid value
        // Should handle gracefully (in real implementation)
        assert.ok(invalidConfig.confidenceThreshold < 0);
        // Test async error handling
        const errorFn = async () => {
            throw new Error("Test error");
        };
        const debouncedErrorFn = createDebouncedFn(errorFn, 100);
        const result = await debouncedErrorFn();
        assert.strictEqual(result, null); // Should return null on error
    });
    // Wait for any async operations
    await new Promise(resolve => setTimeout(resolve, 100));
    // Print results
    console.log("\n" + "=".repeat(50));
    console.log("📊 Integration Test Results:");
    console.log(`  ✅ Passed: ${passedCount}`);
    console.log(`  ❌ Failed: ${failedCount}`);
    console.log(`  📈 Success Rate: ${Math.round((passedCount / testCount) * 100)}%`);
    if (failedCount === 0) {
        console.log("\n🎉 All integration tests passed!");
        return true;
    }
    else {
        console.log(`\n💥 ${failedCount} integration test(s) failed!`);
        return false;
    }
}
// Run integration tests
runIntegrationTests().then((success) => {
    process.exit(success ? 0 : 1);
}).catch((error) => {
    console.error("💥 Error running integration tests:", error);
    process.exit(1);
});
