# 代码补全详细日志功能

## 概述

为了更好地调试和分析代码补全功能，我们在 `AutocompleteProvider.ts` 中添加了详细的日志记录功能。这些日志会记录每次补全请求的完整prompt和模型返回的所有数据，按原始结构输出到console。

## 日志类型

### 1. FIM (Fill-in-Middle) 补全日志

#### 请求日志
```
🚀📝 === FIM COMPLETION REQUEST ===
🚀📝 Request ID: [UUID]
🚀📝 Text Before Cursor:
[完整的光标前文本]
🚀📝 Text After Cursor:
[完整的光标后文本]
🚀📝 === END FIM REQUEST ===
```

#### 结构化请求数据
```json
{
  "requestId": "uuid",
  "timestamp": "ISO时间戳",
  "contextStrategy": "meaningful-parent|inter-declaration|context-window|limited-fallback",
  "contextLines": 100,
  "usedFullFile": false,
  "cursorPosition": {
    "line": 10,
    "character": 20,
    "lineInContext": 5,
    "charInContext": 15
  },
  "textBeforeCursor": "完整文本",
  "textAfterCursor": "完整文本",
  "documentInfo": {
    "fileName": "文件路径",
    "languageId": "typescript",
    "lineCount": 100
  }
}
```

#### 响应日志
```
🚀📥 === FIM COMPLETION RESPONSE ===
🚀📥 Request ID: [UUID]
🚀📥 Raw Model Response:
[模型原始返回内容]
🚀📥 === END FIM RESPONSE ===
```

#### 结构化响应数据
```json
{
  "requestId": "uuid",
  "timestamp": "ISO时间戳",
  "isStreaming": true,
  "chunks": ["chunk1", "chunk2"],
  "totalChunks": 2,
  "rawCompletion": "完整原始响应",
  "completionLength": 150,
  "completionLines": 5
}
```

### 2. 标准API补全日志

#### 请求日志
```
🚀📝 === STANDARD API COMPLETION REQUEST ===
🚀📝 Request ID: [UUID]
🚀📝 System Prompt:
[完整系统提示词]
🚀📝 User Prompt:
[完整用户提示词]
🚀📝 === END STANDARD API REQUEST ===
```

#### 结构化请求数据
```json
{
  "requestId": "uuid",
  "timestamp": "ISO时间戳",
  "systemPrompt": "完整系统提示词",
  "userPrompt": "完整用户提示词",
  "codeContext": {
    "currentLine": "当前行内容",
    "precedingLines": ["前面的行"],
    "followingLines": ["后面的行"],
    "imports": ["导入语句"],
    "definitions": [{"filepath": "", "content": "", "range": {}}],
    "clipboardContent": "剪贴板内容"
  },
  "snippets": [],
  "documentInfo": {
    "fileName": "文件路径",
    "languageId": "typescript",
    "lineCount": 100
  },
  "cursorPosition": {
    "line": 10,
    "character": 20
  }
}
```

#### 流式响应日志
每个chunk都会单独记录：
```json
{
  "type": "text",
  "text": "chunk内容"
}
```

#### 完整响应日志
```
🚀📥 === STANDARD API COMPLETION RESPONSE ===
🚀📥 Request ID: [UUID]
🚀📥 Raw Model Response:
[模型原始返回内容]
🚀📥 Processed Completion:
[处理后的补全内容]
🚀📥 === END STANDARD API RESPONSE ===
```

### 3. 最终处理日志

#### 处理过程日志
```
🚀🎯 === FINAL COMPLETION PROCESSING ===
🚀🎯 Processed Completion:
[处理后的补全内容]
🚀🎯 Line Prefix:
"[当前行前缀]"
🚀🎯 === END FINAL PROCESSING ===
```

#### 结构化最终数据
```json
{
  "timestamp": "ISO时间戳",
  "provider": "fim|standard",
  "originalCompletion": "原始补全",
  "finalCompletion": "最终补全",
  "linePrefix": "行前缀",
  "insertRange": {
    "start": {"line": 10, "character": 0},
    "end": {"line": 10, "character": 20}
  },
  "position": {"line": 10, "character": 20},
  "hasLeadingWhitespace": true,
  "completionLength": 150,
  "completionLines": 5
}
```

### 4. 补全总结日志

#### 总结信息
```
🚀📤 === COMPLETION SUMMARY ===
🚀📤 Provider: fim|standard
🚀📤 Final Completion Text:
[最终补全文本]
🚀📤 Completion Item Command: [命令信息]
🚀📤 Insert Range: [插入范围]
🚀📤 === END COMPLETION SUMMARY ===
```

#### 完整周期总结
```json
{
  "timestamp": "ISO时间戳",
  "provider": "fim|standard",
  "documentInfo": {
    "fileName": "文件路径",
    "languageId": "typescript",
    "lineCount": 100
  },
  "cursorPosition": {"line": 10, "character": 20},
  "linePrefix": "行前缀",
  "finalCompletion": "最终补全",
  "completionLength": 150,
  "completionLines": 5,
  "insertRange": {
    "start": {"line": 10, "character": 0},
    "end": {"line": 10, "character": 20}
  },
  "totalCompletionCalls": 42,
  "totalAcceptedSuggestions": 15
}
```

### 5. 错误日志

#### FIM错误
```json
{
  "requestId": "uuid",
  "timestamp": "ISO时间戳",
  "error": {
    "name": "错误名称",
    "message": "错误消息",
    "stack": "错误堆栈"
  },
  "chunksReceived": 3,
  "partialCompletion": "部分补全内容",
  "textBeforeCursor": "光标前文本(截断到500字符)",
  "textAfterCursor": "光标后文本(截断到500字符)"
}
```

#### 标准API错误
```json
{
  "requestId": "uuid",
  "timestamp": "ISO时间戳",
  "error": {
    "name": "错误名称",
    "message": "错误消息",
    "stack": "错误堆栈"
  },
  "chunksReceived": 5,
  "partialCompletion": "部分补全内容"
}
```

## 使用方法

1. **开启日志**：日志默认开启，所有补全请求都会记录
2. **查看日志**：打开VS Code的开发者工具 (Ctrl+Shift+I)，查看Console面板
3. **过滤日志**：使用以下前缀过滤特定类型的日志：
   - `🚀📝` - 请求日志
   - `🚀📥` - 响应日志
   - `🚀🎯` - 处理日志
   - `🚀📤` - 总结日志
   - `🚀❌` - 错误日志
   - `🚀📊` - 结构化数据

## 调试技巧

1. **追踪特定请求**：使用Request ID在日志中追踪完整的请求-响应周期
2. **分析prompt质量**：检查系统提示词和用户提示词是否包含足够的上下文
3. **检查模型响应**：对比原始响应和处理后的补全，确认处理逻辑是否正确
4. **性能分析**：通过时间戳分析请求处理时间
5. **错误诊断**：查看错误日志中的详细信息和部分响应

## 注意事项

- 日志可能包含敏感的代码内容，在生产环境中请谨慎处理
- 大量的日志输出可能影响性能，建议在调试时使用
- 结构化数据使用JSON格式，便于程序化分析
