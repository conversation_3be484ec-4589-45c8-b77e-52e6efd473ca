/**
 * Simple test runner for QaxNextEdit without external dependencies
 */

import * as assert from "assert"
import * as vscode from "vscode"

// Mock VS Code API
const mockVscode = {
	workspace: {
		onDidChangeTextDocument: () => ({ dispose: () => {} }),
		onDidOpenTextDocument: () => ({ dispose: () => {} }),
		onDidCloseTextDocument: () => ({ dispose: () => {} }),
		onDidChangeConfiguration: () => ({ dispose: () => {} }),
		getConfiguration: () => ({
			get: (key: string, defaultValue?: any) => defaultValue,
			update: () => Promise.resolve()
		}),
		openTextDocument: async (options: any) => ({
			uri: { fsPath: options.content ? "mock.ts" : "test.ts" },
			languageId: options.language || "typescript",
			getText: () => options.content || "mock content",
			offsetAt: (position: vscode.Position) => position.line * 100 + position.character
		}),
		findTextInFiles: async () => new Map()
	},
	window: {
		onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
		createStatusBarItem: () => ({
			text: "",
			show: () => {},
			hide: () => {},
			dispose: () => {}
		}),
		createTextEditorDecorationType: () => ({
			dispose: () => {}
		}),
		showTextDocument: async (document: any) => ({
			document,
			setDecorations: () => {},
			selection: new vscode.Selection(0, 0, 0, 0),
			revealRange: () => {}
		}),
		showInformationMessage: () => Promise.resolve(),
		showWarningMessage: () => Promise.resolve(),
		showErrorMessage: () => Promise.resolve(),
		showQuickPick: () => Promise.resolve()
	},
	languages: {
		onDidChangeDiagnostics: () => ({ dispose: () => {} }),
		registerHoverProvider: () => ({ dispose: () => {} })
	},
	commands: {
		executeCommand: async () => null,
		registerCommand: () => ({ dispose: () => {} })
	},
	StatusBarAlignment: { Right: 2 },
	TextEditorRevealType: { InCenter: 1 },
	SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
	ConfigurationTarget: { Global: 1 },
	ThemeColor: class { constructor(public id: string) {} },
	MarkdownString: class { 
		constructor(public value: string = "") { 
			this.isTrusted = false 
		}
		isTrusted = false
		appendMarkdown(value: string) { this.value += value; return this }
		appendCodeblock(value: string, language?: string) { 
			this.value += `\n\`\`\`${language || ''}\n${value}\n\`\`\`\n`
			return this 
		}
	},
	Hover: class { constructor(public contents: any, public range?: any) {} },
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" })
	}
}

// Apply mocks
Object.assign(vscode, mockVscode)

// Import services after mocking
import { QaxNextEditService } from "../QaxNextEditService"
import { QaxLSPService } from "../services/QaxLSPService"
import { QaxASTService } from "../services/QaxASTService"
import { QaxChangeDetector } from "../services/QaxChangeDetector"
import { QaxJumpSuggestionEngine } from "../services/QaxJumpSuggestionEngine"
import { QaxNextEditUIProvider } from "../QaxNextEditUIProvider"
import { DEFAULT_QAX_NEXT_EDIT_CONFIG, QaxChangeType } from "../types/QaxNextEditTypes"

// Test utilities
class TestRunner {
	private tests: Array<{ name: string, fn: () => Promise<void> | void }> = []
	private passed = 0
	private failed = 0
	private errors: Array<{ test: string, error: Error }> = []

	test(name: string, fn: () => Promise<void> | void) {
		this.tests.push({ name, fn })
	}

	async run() {
		console.log(`🚀 Running ${this.tests.length} tests...\n`)

		for (const test of this.tests) {
			try {
				console.log(`  ▶ ${test.name}`)
				await test.fn()
				console.log(`  ✅ ${test.name}`)
				this.passed++
			} catch (error) {
				console.log(`  ❌ ${test.name}`)
				console.log(`     ${error.message}`)
				this.failed++
				this.errors.push({ test: test.name, error: error as Error })
			}
		}

		console.log(`\n📊 Test Results:`)
		console.log(`  ✅ Passed: ${this.passed}`)
		console.log(`  ❌ Failed: ${this.failed}`)
		console.log(`  📈 Coverage: ${Math.round((this.passed / this.tests.length) * 100)}%`)

		if (this.errors.length > 0) {
			console.log(`\n💥 Errors:`)
			this.errors.forEach(({ test, error }) => {
				console.log(`  ${test}: ${error.message}`)
				if (error.stack) {
					console.log(`    ${error.stack.split('\n').slice(1, 3).join('\n    ')}`)
				}
			})
		}

		return this.failed === 0
	}
}

// Create test runner
const runner = new TestRunner()

// QaxNextEditService Tests
runner.test("QaxNextEditService - should create singleton instance", () => {
	QaxNextEditService.dispose()
	const instance1 = QaxNextEditService.getInstance()
	const instance2 = QaxNextEditService.getInstance()
	assert.strictEqual(instance1, instance2)
	instance1.dispose()
})

runner.test("QaxNextEditService - should initialize with default config", () => {
	QaxNextEditService.dispose()
	const service = QaxNextEditService.getInstance()
	const config = service.getConfig()
	assert.strictEqual(config.enabled, true)
	assert.strictEqual(config.enableLSPIntegration, true)
	assert.strictEqual(config.enableASTAnalysis, true)
	service.dispose()
})

runner.test("QaxNextEditService - should enable/disable correctly", () => {
	QaxNextEditService.dispose()
	const service = QaxNextEditService.getInstance()
	
	service.setEnabled(false)
	assert.strictEqual(service.getState().isEnabled, false)
	
	service.setEnabled(true)
	assert.strictEqual(service.getState().isEnabled, true)
	
	service.dispose()
})

runner.test("QaxNextEditService - should update config", () => {
	QaxNextEditService.dispose()
	const service = QaxNextEditService.getInstance()
	
	service.updateConfig({ debounceDelayMs: 3000 })
	const config = service.getConfig()
	assert.strictEqual(config.debounceDelayMs, 3000)
	
	service.dispose()
})

runner.test("QaxNextEditService - should handle empty analysis results", () => {
	QaxNextEditService.dispose()
	const service = QaxNextEditService.getInstance()
	
	const result = service.getAnalysisResult("non-existent.ts")
	assert.strictEqual(result, null)
	
	const suggestions = service.getJumpSuggestions("non-existent.ts")
	assert.strictEqual(suggestions.length, 0)
	
	service.dispose()
})

// QaxLSPService Tests
runner.test("QaxLSPService - should create singleton instance", () => {
	QaxLSPService.dispose()
	const instance1 = QaxLSPService.getInstance()
	const instance2 = QaxLSPService.getInstance()
	assert.strictEqual(instance1, instance2)
	instance1.dispose()
})

runner.test("QaxLSPService - should handle empty document symbols", async () => {
	QaxLSPService.dispose()
	const service = QaxLSPService.getInstance()
	const mockDocument = {
		uri: { fsPath: "test.ts" }
	} as vscode.TextDocument

	const symbols = await service.getDocumentSymbols(mockDocument)
	assert.strictEqual(symbols.length, 0)
	service.dispose()
})

runner.test("QaxLSPService - should handle empty references", async () => {
	QaxLSPService.dispose()
	const service = QaxLSPService.getInstance()
	const mockDocument = {
		uri: { fsPath: "test.ts" }
	} as vscode.TextDocument
	const position = new vscode.Position(0, 5)

	const references = await service.getReferences(mockDocument, position)
	assert.strictEqual(references.length, 0)
	service.dispose()
})

// QaxChangeDetector Tests
runner.test("QaxChangeDetector - should initialize with config", () => {
	const detector = new QaxChangeDetector(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	assert.ok(detector)
})

runner.test("QaxChangeDetector - should handle unsupported language", async () => {
	const detector = new QaxChangeDetector(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	const mockDocument = {
		uri: { fsPath: "test.txt" },
		languageId: "plaintext",
		getText: () => "plain text",
		offsetAt: (position: vscode.Position) => position.line * 100 + position.character
	} as vscode.TextDocument

	const context = {
		filePath: "test.txt",
		document: mockDocument,
		changes: [],
		beforeContent: "plain text",
		afterContent: "plain text",
		languageId: "plaintext"
	}

	const result = await detector.analyzeChanges(context)
	assert.strictEqual(result.detectedChanges.length, 0)
	assert.strictEqual(result.confidence, 0)
})

// QaxJumpSuggestionEngine Tests
runner.test("QaxJumpSuggestionEngine - should initialize with config", () => {
	const engine = new QaxJumpSuggestionEngine(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	assert.ok(engine)
})

runner.test("QaxJumpSuggestionEngine - should handle empty changes", async () => {
	const engine = new QaxJumpSuggestionEngine(DEFAULT_QAX_NEXT_EDIT_CONFIG)
	const context = {
		filePath: "test.ts",
		document: {} as vscode.TextDocument,
		changes: [],
		beforeContent: "",
		afterContent: "",
		languageId: "typescript"
	}

	const suggestions = await engine.generateJumpSuggestions([], context)
	assert.strictEqual(suggestions.length, 0)
})

// QaxNextEditUIProvider Tests
runner.test("QaxNextEditUIProvider - should initialize correctly", () => {
	const uiProvider = new QaxNextEditUIProvider()
	assert.ok(uiProvider)
	uiProvider.dispose()
})

runner.test("QaxNextEditUIProvider - should handle empty suggestions", async () => {
	const uiProvider = new QaxNextEditUIProvider()
	await uiProvider.showSuggestions([])
	
	const current = uiProvider.getCurrentSuggestion()
	assert.strictEqual(current, null)
	
	uiProvider.dispose()
})

runner.test("QaxNextEditUIProvider - should handle navigation with no suggestions", () => {
	const uiProvider = new QaxNextEditUIProvider()
	
	// Should not throw errors
	uiProvider.navigateToNextSuggestion()
	uiProvider.navigateToPreviousSuggestion()
	
	uiProvider.dispose()
})

// Type Tests
runner.test("Types - should have correct default config", () => {
	assert.strictEqual(DEFAULT_QAX_NEXT_EDIT_CONFIG.enabled, true)
	assert.strictEqual(DEFAULT_QAX_NEXT_EDIT_CONFIG.enableLSPIntegration, true)
	assert.strictEqual(DEFAULT_QAX_NEXT_EDIT_CONFIG.enableASTAnalysis, true)
	assert.strictEqual(DEFAULT_QAX_NEXT_EDIT_CONFIG.debounceDelayMs, 1500)
	assert.strictEqual(DEFAULT_QAX_NEXT_EDIT_CONFIG.maxSuggestions, 8)
	assert.strictEqual(DEFAULT_QAX_NEXT_EDIT_CONFIG.confidenceThreshold, 0.7)
})

runner.test("Types - should have correct change types", () => {
	assert.strictEqual(QaxChangeType.VARIABLE_RENAME, "variable_rename")
	assert.strictEqual(QaxChangeType.FUNCTION_PARAMETER_CHANGE, "function_parameter_change")
	assert.strictEqual(QaxChangeType.FUNCTION_CALL_DELETION, "function_call_deletion")
	assert.strictEqual(QaxChangeType.VARIABLE_DELETION, "variable_deletion")
})

// Integration Tests
runner.test("Integration - should work together", async () => {
	QaxNextEditService.dispose()
	const service = QaxNextEditService.getInstance()
	const uiProvider = new QaxNextEditUIProvider()
	
	// Should initialize without errors
	assert.ok(service)
	assert.ok(uiProvider)
	
	// Should handle basic operations
	service.setEnabled(true)
	assert.strictEqual(service.getState().isEnabled, true)
	
	await uiProvider.showSuggestions([])
	assert.strictEqual(uiProvider.getCurrentSuggestion(), null)
	
	service.dispose()
	uiProvider.dispose()
})

// Run all tests
async function runTests() {
	const success = await runner.run()
	
	if (success) {
		console.log("\n🎉 All tests passed!")
		process.exit(0)
	} else {
		console.log("\n💥 Some tests failed!")
		process.exit(1)
	}
}

// Handle errors
process.on('uncaughtException', (error) => {
	console.error('💥 Uncaught exception:', error)
	process.exit(1)
})

process.on('unhandledRejection', (reason) => {
	console.error('💥 Unhandled rejection:', reason)
	process.exit(1)
})

// Run tests
runTests().catch((error) => {
	console.error('💥 Error running tests:', error)
	process.exit(1)
})
