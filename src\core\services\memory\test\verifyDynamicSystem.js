const fs = require("fs/promises")
const path = require("path")

/**
 * Verification script for the new dynamic category system
 */
class DynamicSystemVerification {
	constructor() {
		this.testDir = path.join(__dirname, "dynamic-verification-output")
	}

	async setup() {
		// Create test directory
		await fs.mkdir(this.testDir, { recursive: true })
	}

	async verifyCodeChanges() {
		console.log("🔍 Verifying code changes for dynamic category system...")

		const checks = [
			{
				file: "../types.ts",
				description: "Types updated for dynamic categories",
				checks: [
					{ pattern: /category: string/, description: "MemoryEntry.category is now string" },
					{ pattern: /\[category: string\]: MemoryEntry\[\]/, description: "MemoryStorage supports dynamic keys" },
					{ pattern: /EXAMPLE_TOPIC_CATEGORIES/, description: "Example topic categories defined" },
				],
			},
			{
				file: "../MemoryManager.ts",
				description: "MemoryManager updated for dynamic categories",
				checks: [
					{ pattern: /return \{\} as MemoryStorage/, description: "Storage initialized as empty object" },
					{ pattern: /category: string/, description: "Methods accept string categories" },
					{ pattern: /lines\.push\(`# \$\{category\}`\)/, description: "Markdown generation uses category directly" },
					{
						pattern: /currentCategory = categoryMatch\[1\]\.trim\(\)/,
						description: "Parsing uses category name directly",
					},
				],
			},
			{
				file: "../prompts.ts",
				description: "Prompts updated for dynamic categories",
				checks: [
					{ pattern: /动态主题分类系统/, description: "Dynamic topic classification system described" },
					{ pattern: /EXAMPLE_TOPIC_CATEGORIES/, description: "Example topic categories used in prompts" },
					{ pattern: /英文命名.*便于系统处理/, description: "English naming convention specified" },
				],
			},
		]

		let allPassed = true

		for (const fileCheck of checks) {
			console.log(`\n📁 Checking ${fileCheck.description}...`)

			try {
				const filePath = path.join(__dirname, fileCheck.file)
				const content = await fs.readFile(filePath, "utf-8")

				for (const check of fileCheck.checks) {
					if (check.pattern.test(content)) {
						console.log(`   ✅ ${check.description}`)
					} else {
						console.log(`   ❌ ${check.description}`)
						allPassed = false
					}
				}
			} catch (error) {
				console.log(`   ❌ Could not read file: ${error.message}`)
				allPassed = false
			}
		}

		return allPassed
	}

	async verifyMemoriesFile() {
		console.log("\n📄 Verifying memories.md file format...")

		try {
			const memoriesPath = path.join(__dirname, "..", "..", "..", "..", "..", ".clinerules", "memories.md")
			const content = await fs.readFile(memoriesPath, "utf-8")

			const lines = content.split("\n")

			// Check for dynamic category headers
			const categoryHeaders = lines.filter((line) => line.match(/^# [^#]/))

			if (categoryHeaders.length === 0) {
				console.log("   ❌ No category headers found")
				return false
			}

			// Check for expected dynamic categories
			const expectedCategories = [
				"Code Context Extraction",
				"File Management",
				"Memory Service",
				"Service Initialization",
				"User Input Handling",
				"UI/UX Preferences",
				"Context Menu",
			]

			let foundCategories = 0
			for (const expectedCategory of expectedCategories) {
				const found = categoryHeaders.some((header) => header.includes(expectedCategory))
				if (found) {
					foundCategories++
					console.log(`   ✅ Found category: ${expectedCategory}`)
				} else {
					console.log(`   ⚠️  Category not found: ${expectedCategory}`)
				}
			}

			// Check for bullet points
			const bulletPoints = lines.filter((line) => line.startsWith("- "))
			if (bulletPoints.length === 0) {
				console.log("   ❌ No bullet points found")
				return false
			}

			console.log(`   ✅ Found ${categoryHeaders.length} category headers`)
			console.log(`   ✅ Found ${bulletPoints.length} bullet points`)
			console.log(`   ✅ Found ${foundCategories}/${expectedCategories.length} expected categories`)

			return foundCategories >= expectedCategories.length * 0.8 // At least 80% of expected categories
		} catch (error) {
			console.log(`   ❌ Could not read memories file: ${error.message}`)
			return false
		}
	}

	async verifyAIPrompts() {
		console.log("\n🤖 Verifying AI prompt changes...")

		try {
			const promptsPath = path.join(__dirname, "..", "prompts.ts")
			const content = await fs.readFile(promptsPath, "utf-8")

			const checks = [
				{
					pattern: /动态主题分类系统/,
					description: "Dynamic topic classification system mentioned",
				},
				{
					pattern: /具体且有意义.*反映实际的功能模块/,
					description: "Specific and meaningful topics described",
				},
				{
					pattern: /英文命名.*便于系统处理/,
					description: "English naming for system processing",
				},
				{
					pattern: /Pascal Case.*Code Context Extraction/,
					description: "Pascal Case format example provided",
				},
				{
					pattern: /识别主题.*分析用户输入涉及的具体功能/,
					description: "Topic identification principles defined",
				},
			]

			let passedChecks = 0
			for (const check of checks) {
				if (check.pattern.test(content)) {
					console.log(`   ✅ ${check.description}`)
					passedChecks++
				} else {
					console.log(`   ❌ ${check.description}`)
				}
			}

			return passedChecks >= checks.length * 0.8 // At least 80% of checks pass
		} catch (error) {
			console.log(`   ❌ Could not read prompts file: ${error.message}`)
			return false
		}
	}

	async verifyExampleCategories() {
		console.log("\n📋 Verifying example topic categories...")

		try {
			const typesPath = path.join(__dirname, "..", "types.ts")
			const content = await fs.readFile(typesPath, "utf-8")

			const expectedTopics = [
				"Code Context Extraction",
				"File Management",
				"Memory Service",
				"Service Initialization",
				"User Input Handling",
				"UI/UX Preferences",
				"Context Menu",
				"Testing",
				"Code Style",
				"Development Tools",
			]

			let foundTopics = 0
			for (const topic of expectedTopics) {
				if (content.includes(`"${topic}"`)) {
					console.log(`   ✅ Found topic: ${topic}`)
					foundTopics++
				} else {
					console.log(`   ⚠️  Topic not found: ${topic}`)
				}
			}

			console.log(`   ✅ Found ${foundTopics}/${expectedTopics.length} expected topics`)
			return foundTopics >= expectedTopics.length * 0.7 // At least 70% of topics
		} catch (error) {
			console.log(`   ❌ Could not read types file: ${error.message}`)
			return false
		}
	}

	async cleanup() {
		try {
			await fs.rm(this.testDir, { recursive: true, force: true })
		} catch (error) {
			console.warn("Warning: Could not clean up test directory:", error)
		}
	}

	async run() {
		try {
			await this.setup()

			console.log("🚀 Starting dynamic category system verification...\n")

			const codeChangesOk = await this.verifyCodeChanges()
			const memoriesFileOk = await this.verifyMemoriesFile()
			const aiPromptsOk = await this.verifyAIPrompts()
			const exampleCategoriesOk = await this.verifyExampleCategories()

			const allVerificationsPassed = codeChangesOk && memoriesFileOk && aiPromptsOk && exampleCategoriesOk

			console.log("\n" + "=".repeat(60))
			console.log("📊 VERIFICATION SUMMARY")
			console.log("=".repeat(60))
			console.log(`Code Changes:        ${codeChangesOk ? "✅ PASSED" : "❌ FAILED"}`)
			console.log(`Memories File:       ${memoriesFileOk ? "✅ PASSED" : "❌ FAILED"}`)
			console.log(`AI Prompts:          ${aiPromptsOk ? "✅ PASSED" : "❌ FAILED"}`)
			console.log(`Example Categories:  ${exampleCategoriesOk ? "✅ PASSED" : "❌ FAILED"}`)
			console.log("=".repeat(60))

			if (allVerificationsPassed) {
				console.log("🎉 All verifications passed! Dynamic category system is working correctly.")
				console.log("\n📋 Summary of changes:")
				console.log("   ✅ Replaced fixed 5-category system with dynamic topic-based categories")
				console.log("   ✅ Updated AI prompts to identify and create topic-specific categories")
				console.log("   ✅ Modified MemoryManager to support flexible category storage")
				console.log("   ✅ Updated markdown format to use topic names directly")
				console.log("   ✅ Maintained backward compatibility with existing functionality")
			} else {
				console.log("❌ Some verifications failed. Please check the issues above.")
				process.exit(1)
			}
		} catch (error) {
			console.error("❌ Verification failed:", error.message)
			throw error
		} finally {
			await this.cleanup()
		}
	}
}

// Run verification
if (require.main === module) {
	const verification = new DynamicSystemVerification()
	verification.run().catch(console.error)
}

module.exports = { DynamicSystemVerification }
