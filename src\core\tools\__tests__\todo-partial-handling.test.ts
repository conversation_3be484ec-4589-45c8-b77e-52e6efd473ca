import { describe, it, expect, beforeEach, vi } from "vitest"

describe("Todo List Partial Handling Tests", () => {
	let mockTaskState: any
	let mockAskApproval: any
	let mockHandleError: any
	let mockPushToolResult: any
	let mockRemoveClosingTag: any
	let toolResults: string[]
	let askCalls: any[]

	beforeEach(() => {
		toolResults = []
		askCalls = []

		mockTaskState = {
			todoList: [],
			consecutiveMistakeCount: 0,
			say: vi.fn(),
		}

		mockAskApproval = vi.fn().mockResolvedValue(true)
		mockHandleError = vi.fn()
		mockPushToolResult = vi.fn((result: string) => {
			toolResults.push(result)
		})
		mockRemoveClosingTag = vi.fn((block: any, param: string, value?: string) => {
			// Simulate removeClosingTag behavior for partial content
			if (value === undefined) return ""
			return value
		})
	})

	describe("Partial Block Handling", () => {
		it("should handle partial block with empty todos parameter", async () => {
			// Simulate the first call when only the opening tag is received
			const partialBlock = {
				name: "update_todo_list",
				params: {}, // Empty params initially
				partial: true,
			}

			// This should not throw an error and should not call updateTodoListTool
			// In the fixed implementation, this is handled in ToolExecutor before calling updateTodoListTool
			expect(() => {
				// The ToolExecutor would handle this case and not call updateTodoListTool
				// So we simulate what ToolExecutor does:
				if (partialBlock.partial) {
					const partialMessage = JSON.stringify({
						tool: "updateTodoList",
						todos: mockRemoveClosingTag(partialBlock, "todos", (partialBlock.params as any).todos),
					})
					// This would call ask() but not updateTodoListTool
					askCalls.push({ type: "tool", message: partialMessage, partial: true })
				}
			}).not.toThrow()

			expect(askCalls).toHaveLength(1)
			expect(askCalls[0].partial).toBe(true)
		})

		it("should handle partial block with incomplete todos parameter", async () => {
			// Simulate receiving partial todos content
			const partialBlock = {
				name: "update_todo_list",
				params: {
					todos: "- [ ] Task 1\n- [x] Task", // Incomplete content
				},
				partial: true,
			}

			// This should be handled by ToolExecutor, not updateTodoListTool
			if (partialBlock.partial) {
				const partialMessage = JSON.stringify({
					tool: "updateTodoList",
					todos: mockRemoveClosingTag(partialBlock, "todos", partialBlock.params.todos),
				})
				askCalls.push({ type: "tool", message: partialMessage, partial: true })
			}

			expect(askCalls).toHaveLength(1)
			expect(askCalls[0].message).toContain("Task 1")
			expect(askCalls[0].partial).toBe(true)
		})

		it("should only execute updateTodoListTool when block is complete", async () => {
			const { updateTodoListTool } = await import("../updateTodoListTool")

			// Complete block should execute the tool
			const completeBlock = {
				name: "update_todo_list",
				params: {
					todos: "- [ ] Task 1\n- [x] Task 2 completed",
				},
				partial: false,
			}

			await updateTodoListTool(mockTaskState, completeBlock, mockHandleError, mockPushToolResult)

			expect(mockTaskState.todoList).toHaveLength(2)
			expect(toolResults[0]).toBe("Todo list updated successfully.")
		})
	})

	describe("ToolExecutor Integration Simulation", () => {
		it("should simulate the correct flow for streaming todo content", async () => {
			const { updateTodoListTool } = await import("../updateTodoListTool")

			// Simulate the streaming flow:

			// 1. First call: only opening tag received
			const step1Block = {
				name: "update_todo_list",
				params: {},
				partial: true,
			}

			// ToolExecutor handles this - no call to updateTodoListTool
			if (step1Block.partial) {
				askCalls.push({ step: 1, handled: "by_tool_executor" })
			}

			// 2. Second call: partial todos content
			const step2Block = {
				name: "update_todo_list",
				params: {
					todos: "- [ ] Task 1",
				},
				partial: true,
			}

			// ToolExecutor handles this - no call to updateTodoListTool
			if (step2Block.partial) {
				askCalls.push({ step: 2, handled: "by_tool_executor" })
			}

			// 3. Third call: complete content
			const step3Block = {
				name: "update_todo_list",
				params: {
					todos: "- [ ] Task 1\n- [x] Task 2 completed",
				},
				partial: false,
			}

			// Only now should updateTodoListTool be called
			if (!step3Block.partial) {
				await updateTodoListTool(mockTaskState, step3Block, mockHandleError, mockPushToolResult)
			}

			// Verify the flow
			expect(askCalls).toHaveLength(2) // Two partial calls handled by ToolExecutor
			expect(mockTaskState.todoList).toHaveLength(2) // Final execution created todos
			expect(toolResults[0]).toBe("Todo list updated successfully.")
		})
	})

	describe("Error Handling", () => {
		it("should handle missing todos parameter in complete block", async () => {
			const { updateTodoListTool } = await import("../updateTodoListTool")

			const completeBlockWithoutTodos = {
				name: "update_todo_list",
				params: {}, // No todos parameter
				partial: false,
			}

			await updateTodoListTool(mockTaskState, completeBlockWithoutTodos, mockHandleError, mockPushToolResult)

			expect(mockTaskState.consecutiveMistakeCount).toBe(1)
			expect(toolResults[0]).toBe("The todos parameter is not valid markdown checklist")
		})

		it("should handle invalid markdown in complete block", async () => {
			const { updateTodoListTool } = await import("../updateTodoListTool")

			const completeBlockWithInvalidMarkdown = {
				name: "update_todo_list",
				params: {
					todos: "invalid markdown content",
				},
				partial: false,
			}

			await updateTodoListTool(mockTaskState, completeBlockWithInvalidMarkdown, mockHandleError, mockPushToolResult)

			expect(mockTaskState.consecutiveMistakeCount).toBe(1)
			expect(toolResults[0]).toBe("The todos parameter is not valid markdown checklist")
		})
	})

	describe("Performance and Efficiency", () => {
		it("should not perform expensive operations on partial blocks", () => {
			// This test verifies that partial blocks don't trigger expensive operations
			// In the fixed implementation, ToolExecutor handles partial blocks efficiently

			const partialBlock = {
				name: "update_todo_list",
				params: { todos: "- [ ] Partial content" },
				partial: true,
			}

			// Simulate ToolExecutor behavior
			const startTime = Date.now()

			if (partialBlock.partial) {
				// Only lightweight operations should happen
				const partialMessage = JSON.stringify({
					tool: "updateTodoList",
					todos: mockRemoveClosingTag(partialBlock, "todos", partialBlock.params.todos),
				})
				askCalls.push({ message: partialMessage, partial: true })
			}

			const endTime = Date.now()
			const executionTime = endTime - startTime

			// Should be very fast (less than 10ms for partial handling)
			expect(executionTime).toBeLessThan(10)
			expect(askCalls).toHaveLength(1)
		})
	})
})
