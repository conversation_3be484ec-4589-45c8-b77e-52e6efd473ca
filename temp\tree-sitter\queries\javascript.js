"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.importQuery = exports.definitionQuery = void 0;
/*
- class definitions
- method definitions
- named function declarations
- arrow functions and function expressions assigned to variables
*/
exports.definitionQuery = `
(
  (comment)* @doc
  .
  (method_definition
    name: (property_identifier) @name) @definition.method
  (#not-eq? @name "constructor")
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.method)
)

(
  (comment)* @doc
  .
  [
    (class
      name: (_) @name)
    (class_declaration
      name: (_) @name)
  ] @definition.class
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.class)
)

(
  (comment)* @doc
  .
  [
    (function_declaration
      name: (identifier) @name)
    (generator_function_declaration
      name: (identifier) @name)
  ] @definition.function
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

(
  (comment)* @doc
  .
  (lexical_declaration
    (variable_declarator
      name: (identifier) @name
      value: [(arrow_function) (function_expression)]) @definition.function)
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)

(
  (comment)* @doc
  .
  (variable_declaration
    (variable_declarator
      name: (identifier) @name
      value: [(arrow_function) (function_expression)]) @definition.function)
  (#strip! @doc "^[\\s\\*/]+|^[\\s\\*/]$")
  (#select-adjacent! @doc @definition.function)
)
`;
/*
- JavaScript/TypeScript import statements
- Named imports, default imports, namespace imports
- CommonJS require statements
*/
exports.importQuery = `
; Named imports: import { foo, bar } from 'module'
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @import.name))) @import.statement
  source: (string) @import.source) @import.full_statement

; Named imports with alias: import { foo as bar } from 'module'
(import_statement
  (import_clause
    (named_imports
      (import_specifier
        name: (identifier) @import.name
        alias: (identifier) @import.alias))) @import.statement
  source: (string) @import.source) @import.full_statement

; Default imports: import foo from 'module'
(import_statement
  (import_clause
    (identifier) @import.name) @import.statement
  source: (string) @import.source) @import.full_statement

; Namespace imports: import * as foo from 'module'
(import_statement
  (import_clause
    (namespace_import
      (identifier) @import.namespace_name)) @import.statement
  source: (string) @import.source) @import.namespace_statement

; Side-effect imports: import 'module'
(import_statement
  source: (string) @import.source) @import.side_effect_statement

; CommonJS require: const foo = require('module')
(lexical_declaration
  (variable_declarator
    name: (identifier) @import.name
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.require_statement
  (#eq? @import.method "require")

; CommonJS require with var: var foo = require('module')
(variable_declaration
  (variable_declarator
    name: (identifier) @import.name
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.require_statement
  (#eq? @import.method "require")

; CommonJS destructuring: const { foo, bar } = require('module')
(lexical_declaration
  (variable_declarator
    name: (object_pattern
      (shorthand_property_identifier_pattern) @import.name)
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.statement
  (#eq? @import.method "require")

; CommonJS destructuring with var: var { foo, bar } = require('module')
(variable_declaration
  (variable_declarator
    name: (object_pattern
      (shorthand_property_identifier_pattern) @import.name)
    value: (call_expression
      function: (identifier) @import.method
      arguments: (arguments (string) @import.source)))) @import.statement
  (#eq? @import.method "require")
`;
// Default export for backward compatibility
exports.default = exports.definitionQuery;
