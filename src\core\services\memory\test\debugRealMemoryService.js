/**
 * Debug script to test the real MemoryService integration
 */

const fs = require("fs").promises
const path = require("path")
const os = require("os")

// Check if the actual memory file exists and what's in it
async function debugRealMemoryService() {
	console.log("🔍 Debugging real MemoryService integration...")
	console.log("=".repeat(60))

	// Check current working directory
	const cwd = process.cwd()
	console.log("🔍 Current working directory:", cwd)

	// Look for .clinerules directory in various locations
	const possiblePaths = [
		path.join(cwd, ".clinerules", "memories.md"),
		path.join(cwd, "..", ".clinerules", "memories.md"),
		path.join(cwd, "..", "..", ".clinerules", "memories.md"),
		path.join(cwd, "..", "..", "..", ".clinerules", "memories.md"),
		path.join(cwd, "..", "..", "..", "..", ".clinerules", "memories.md"),
		path.join("/Users/<USER>/codegen/cline", ".clinerules", "memories.md"),
	]

	console.log("\n🔍 Checking for memory files in possible locations:")

	for (const filePath of possiblePaths) {
		try {
			const exists = await fs
				.access(filePath)
				.then(() => true)
				.catch(() => false)
			if (exists) {
				console.log(`✅ Found: ${filePath}`)

				const content = await fs.readFile(filePath, "utf-8")
				console.log(`📄 File size: ${content.length} characters`)
				console.log(`📄 Content preview:`)
				console.log(content.substring(0, 500))
				console.log("...")

				// Check if it contains the recent entries
				if (content.includes("Protobuf") || content.includes("vscode message")) {
					console.log("🎯 Found recent entries in this file!")
				} else {
					console.log("⚠️  No recent entries found in this file")
				}

				console.log("-".repeat(40))
			} else {
				console.log(`❌ Not found: ${filePath}`)
			}
		} catch (error) {
			console.log(`❌ Error checking ${filePath}:`, error.message)
		}
	}

	// Check VS Code settings that might affect memory service
	console.log("\n🔍 Checking potential configuration issues:")

	// Look for VS Code settings
	const settingsPaths = [
		path.join(cwd, ".vscode", "settings.json"),
		path.join(cwd, "..", ".vscode", "settings.json"),
		path.join(cwd, "..", "..", ".vscode", "settings.json"),
		path.join(cwd, "..", "..", "..", ".vscode", "settings.json"),
		path.join("/Users/<USER>/codegen/cline", ".vscode", "settings.json"),
	]

	for (const settingsPath of settingsPaths) {
		try {
			const exists = await fs
				.access(settingsPath)
				.then(() => true)
				.catch(() => false)
			if (exists) {
				console.log(`✅ Found VS Code settings: ${settingsPath}`)

				const content = await fs.readFile(settingsPath, "utf-8")
				const settings = JSON.parse(content)

				// Check memory-related settings
				const memorySettings = {}
				Object.keys(settings).forEach((key) => {
					if (key.includes("memory") || key.includes("cline.memory")) {
						memorySettings[key] = settings[key]
					}
				})

				if (Object.keys(memorySettings).length > 0) {
					console.log("🔧 Memory-related settings found:")
					console.log(JSON.stringify(memorySettings, null, 2))
				} else {
					console.log("⚠️  No memory-related settings found")
				}
				break
			}
		} catch (error) {
			// Ignore errors for settings files
		}
	}

	// Check if there are any recent log files
	console.log("\n🔍 Looking for recent log files or debug output:")

	const logPaths = [
		path.join(cwd, "logs"),
		path.join(cwd, "..", "logs"),
		path.join(cwd, "..", "..", "logs"),
		path.join("/Users/<USER>/codegen/cline", "logs"),
		path.join(os.tmpdir()),
	]

	for (const logPath of logPaths) {
		try {
			const exists = await fs
				.access(logPath)
				.then(() => true)
				.catch(() => false)
			if (exists) {
				const files = await fs.readdir(logPath)
				const recentFiles = files
					.filter((file) => file.includes("log") || file.includes("debug") || file.includes("memory"))
					.slice(0, 5)

				if (recentFiles.length > 0) {
					console.log(`📁 Found log directory: ${logPath}`)
					console.log(`📄 Recent files: ${recentFiles.join(", ")}`)
				}
			}
		} catch (error) {
			// Ignore errors
		}
	}

	// Provide debugging suggestions
	console.log("\n💡 Debugging suggestions:")
	console.log("1. Check if MemoryService is enabled in VS Code settings")
	console.log("2. Look for error messages in VS Code Developer Console (Help > Toggle Developer Tools)")
	console.log("3. Check if the workspace root is correctly detected")
	console.log("4. Verify that the API handler is working correctly")
	console.log("5. Check if there are any permission issues with file writing")

	// Create a test memory file to verify write permissions
	console.log("\n🔍 Testing file write permissions:")

	const testDir = path.join(cwd, ".clinerules")
	const testFile = path.join(testDir, "test-memory.md")

	try {
		await fs.mkdir(testDir, { recursive: true })
		await fs.writeFile(testFile, "# Test Memory File\n\nThis is a test.", "utf-8")
		console.log("✅ File write test successful")

		// Clean up test file
		await fs.unlink(testFile)
		console.log("✅ Test file cleaned up")
	} catch (error) {
		console.log("❌ File write test failed:", error.message)
		console.log("   This might indicate a permission issue")
	}

	console.log("\n" + "=".repeat(60))
	console.log("🔍 Debug completed")

	console.log("\n📋 Next steps to investigate:")
	console.log("1. Enable VS Code Developer Tools and check console for errors")
	console.log("2. Add more console.log statements to MemoryService.processUserInput")
	console.log("3. Check if the input is actually reaching the memory service")
	console.log("4. Verify the workspace root path is correct")
	console.log("5. Test with a simple input to see if any memory is created")
}

// Run debug
if (require.main === module) {
	debugRealMemoryService().catch((error) => {
		console.error("💥 Debug failed:", error)
		process.exit(1)
	})
}

module.exports = { debugRealMemoryService }
