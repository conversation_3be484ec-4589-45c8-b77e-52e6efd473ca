/**
 * Debug script to test the specific memory update issue
 */

const fs = require("fs").promises
const path = require("path")
const os = require("os")

// Mock the exact response from the logs
const mockAIResponse = {
	shouldUpdate: true,
	instructions: [
		{
			operation: "add",
			category: "tech_stack",
			targetId: "",
			newEntry: {
				summary: "前后端通信使用 Protobuf",
				confidence: 0.95,
				reasoning: "用户明确表示前后端通信使用 Protobuf，这是一个重要的技术栈选择",
			},
			mergeWith: [],
		},
		{
			operation: "add",
			category: "tool_preferences",
			targetId: "",
			newEntry: {
				summary: "不使用 vscode message",
				confidence: 0.9,
				reasoning: "用户明确表示不要使用 vscode message，这是一个工具偏好",
			},
			mergeWith: [],
		},
	],
	reasoning: "用户的新输入包含两个明确的技术选择，分别属于技术栈和工具偏好分类，且与现有记忆内容无冲突，值得添加",
	newEntryClassification: {
		category: "tech_stack",
		summary: "前后端通信使用 Protobuf",
		confidence: 0.95,
		reasoning: "Protobuf 是一种明确的技术选择，属于技术栈分类",
	},
}

// Mock memory categories
const MemoryCategory = {
	TOOL_PREFERENCES: "tool_preferences",
	TECH_STACK: "tech_stack",
	CODE_STYLE: "code_style",
	WORKFLOW: "workflow",
	GENERAL: "general",
}

const MemoryUpdateOperation = {
	ADD: "add",
	UPDATE: "update",
	REMOVE: "remove",
	MERGE: "merge",
}

// Simple memory processor to debug the issue
class DebugMemoryProcessor {
	constructor(workspaceRoot) {
		this.workspaceRoot = workspaceRoot
		this.memoriesFilePath = path.join(workspaceRoot, ".clinerules", "memories.md")
		this.storage = this.initializeStorage()
	}

	initializeStorage() {
		const storage = {}
		Object.values(MemoryCategory).forEach((category) => {
			storage[category] = []
		})
		return storage
	}

	async processUpdateResult(updateResult, originalInput) {
		console.log("🔍 Debug: Processing update result")
		console.log("🔍 shouldUpdate:", updateResult.shouldUpdate)
		console.log("🔍 instructions count:", updateResult.instructions.length)

		if (!updateResult.shouldUpdate) {
			console.log("🔍 No update needed")
			return
		}

		// Apply instructions
		for (let i = 0; i < updateResult.instructions.length; i++) {
			const instruction = updateResult.instructions[i]
			console.log(`🔍 Processing instruction ${i + 1}:`, instruction)

			try {
				await this.applyInstruction(instruction, originalInput)
				console.log(`✅ Instruction ${i + 1} applied successfully`)
			} catch (error) {
				console.error(`❌ Failed to apply instruction ${i + 1}:`, error)
			}
		}

		// Save to file
		try {
			await this.saveMemories()
			console.log("✅ Memories saved to file")
		} catch (error) {
			console.error("❌ Failed to save memories:", error)
		}
	}

	async applyInstruction(instruction, originalInput) {
		switch (instruction.operation) {
			case MemoryUpdateOperation.ADD:
				await this.applyAddInstruction(instruction, originalInput)
				break
			default:
				console.warn("🔍 Unknown operation:", instruction.operation)
		}
	}

	async applyAddInstruction(instruction, originalInput) {
		console.log("🔍 Applying ADD instruction")
		console.log("🔍 Category:", instruction.category)
		console.log("🔍 New entry:", instruction.newEntry)

		if (!instruction.newEntry) {
			console.warn("🔍 ADD instruction missing newEntry")
			return
		}

		const memoryEntry = {
			id: `debug-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
			category: instruction.category,
			summary: instruction.newEntry.summary,
			originalInput: originalInput,
			timestamp: Date.now(),
			confidence: instruction.newEntry.confidence,
		}

		console.log("🔍 Created memory entry:", memoryEntry)

		// Check if category exists in storage
		if (!this.storage[instruction.category]) {
			console.error("🔍 Category not found in storage:", instruction.category)
			console.log("🔍 Available categories:", Object.keys(this.storage))
			return
		}

		const categoryEntries = this.storage[instruction.category]
		console.log("🔍 Current entries in category:", categoryEntries.length)

		categoryEntries.unshift(memoryEntry)
		console.log("🔍 Added entry, new count:", categoryEntries.length)

		// Limit entries per category (max 20)
		if (categoryEntries.length > 20) {
			const removed = categoryEntries.splice(20)
			console.log("🔍 Removed", removed.length, "old entries due to limit")
		}

		console.log("✅ Memory entry added successfully")
	}

	async saveMemories() {
		console.log("🔍 Saving memories to file:", this.memoriesFilePath)

		const markdown = this.generateMarkdown()
		console.log("🔍 Generated markdown length:", markdown.length)

		// Ensure directory exists
		const dir = path.dirname(this.memoriesFilePath)
		await fs.mkdir(dir, { recursive: true })
		console.log("🔍 Directory ensured:", dir)

		await fs.writeFile(this.memoriesFilePath, markdown, "utf-8")
		console.log("✅ Memories saved successfully")
	}

	generateMarkdown() {
		const lines = [
			"# Cline Memories",
			"",
			"这个文件记录了用户的偏好、需求和习惯，帮助Cline更好地理解用户的开发风格。",
			"",
			`最后更新：${new Date().toLocaleString("zh-CN")}`,
			"",
		]

		const categoryMetadata = {
			[MemoryCategory.TOOL_PREFERENCES]: { icon: "🔧", displayName: "工具偏好" },
			[MemoryCategory.TECH_STACK]: { icon: "💻", displayName: "技术栈" },
			[MemoryCategory.CODE_STYLE]: { icon: "📝", displayName: "代码风格" },
			[MemoryCategory.WORKFLOW]: { icon: "⚡", displayName: "工作流程" },
			[MemoryCategory.GENERAL]: { icon: "📋", displayName: "一般信息" },
		}

		Object.entries(this.storage).forEach(([category, entries]) => {
			if (entries.length === 0) return

			const metadata = categoryMetadata[category]
			if (!metadata) {
				console.warn("🔍 No metadata for category:", category)
				return
			}

			lines.push(`## ${metadata.icon} ${metadata.displayName}`)
			lines.push("")

			entries.forEach((entry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		return lines.join("\n")
	}

	getAllMemories() {
		return { ...this.storage }
	}
}

async function debugMemoryUpdate() {
	console.log("🔍 Starting memory update debug...")
	console.log("=".repeat(60))

	// Create temporary test workspace
	const testWorkspaceRoot = await fs.mkdtemp(path.join(os.tmpdir(), "memory-debug-"))
	console.log("🔍 Test workspace:", testWorkspaceRoot)

	try {
		const processor = new DebugMemoryProcessor(testWorkspaceRoot)
		const originalInput = "前后端通信使用 Protobuf，不要使用 vscode message"

		console.log("🔍 Original input:", originalInput)
		console.log("🔍 Mock AI response:", JSON.stringify(mockAIResponse, null, 2))

		// Process the update result
		await processor.processUpdateResult(mockAIResponse, originalInput)

		// Check final state
		const memories = processor.getAllMemories()
		console.log("\n🔍 Final memory state:")
		Object.entries(memories).forEach(([category, entries]) => {
			console.log(`  ${category}: ${entries.length} entries`)
			entries.forEach((entry, index) => {
				console.log(`    ${index + 1}. ${entry.summary} (confidence: ${entry.confidence})`)
			})
		})

		// Check if file was created
		const memoriesPath = path.join(testWorkspaceRoot, ".clinerules", "memories.md")
		const exists = await fs
			.access(memoriesPath)
			.then(() => true)
			.catch(() => false)

		if (exists) {
			const content = await fs.readFile(memoriesPath, "utf-8")
			console.log("\n🔍 Memory file created successfully:")
			console.log("🔍 File size:", content.length, "characters")
			console.log("🔍 File content preview:")
			console.log(content.substring(0, 300) + "...")
		} else {
			console.error("❌ Memory file was not created")
		}
	} finally {
		// Cleanup
		await fs.rm(testWorkspaceRoot, { recursive: true, force: true })
		console.log("🔍 Cleanup completed")
	}

	console.log("\n" + "=".repeat(60))
	console.log("🔍 Debug completed")
}

// Run debug
if (require.main === module) {
	debugMemoryUpdate().catch((error) => {
		console.error("💥 Debug failed:", error)
		process.exit(1)
	})
}

module.exports = { debugMemoryUpdate }
