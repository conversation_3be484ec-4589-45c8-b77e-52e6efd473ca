import * as vscode from "vscode"

/**
 * Configuration for completion filtering
 */
export interface CompletionFilterConfig {
	/** Whether to enable completion filtering */
	enabled: boolean
	/** Whether to filter out completions that are just whitespace */
	filterWhitespaceOnly: boolean
	/** Whether to filter out completions that repeat the current line */
	filterRepeatedContent: boolean
	/** Specific strings to filter out (e.g., "{{FILL_HERE}}") */
	filterStrings: string[]
}

/**
 * Default configuration for completion filtering
 */
export const DEFAULT_COMPLETION_FILTER_CONFIG: CompletionFilterConfig = {
	enabled: true,
	filterWhitespaceOnly: true,
	filterRepeatedContent: true,
	filterStrings: ["{{FILL_HERE}}", "{{TODO}}", "{{PLACEHOLDER}}"],
}

/**
 * Context information for filtering decisions
 */
export interface FilterContext {
	/** The current line content before the cursor */
	linePrefix: string
	/** The current line content after the cursor */
	lineSuffix: string
	/** The language ID of the current document */
	languageId: string
	/** The current document */
	document: vscode.TextDocument
	/** The cursor position */
	position: vscode.Position
}

/**
 * Filters completion results based on various criteria
 */
export class CompletionFilter {
	private config: CompletionFilterConfig

	constructor(config: CompletionFilterConfig = DEFAULT_COMPLETION_FILTER_CONFIG) {
		this.config = config
	}

	/**
	 * Update the filter configuration
	 */
	public updateConfig(config: Partial<CompletionFilterConfig>): void {
		this.config = { ...this.config, ...config }
	}

	/**
	 * Filter a completion result
	 * @param completion The completion text to filter
	 * @param context The context information for filtering
	 * @returns The filtered completion or null if it should be rejected
	 */
	public filterCompletion(completion: string, context: FilterContext): string | null {
		if (!this.config.enabled) {
			return completion
		}

		// Filter whitespace-only completions
		if (this.config.filterWhitespaceOnly && this.isWhitespaceOnly(completion)) {
			console.log("🚀🚫 Completion filtered: whitespace only")
			return null
		}

		// Filter repeated content
		if (this.config.filterRepeatedContent && this.isRepeatedContent(completion, context)) {
			console.log("🚀🚫 Completion filtered: repeated content")
			return null
		}

		// Filter specific strings
		for (const filterString of this.config.filterStrings) {
			if (completion.includes(filterString)) {
				console.log(`🚀🚫 Completion filtered: contains "${filterString}"`)
				return null
			}
		}

		return completion
	}

	/**
	 * Check if completion is only whitespace
	 */
	private isWhitespaceOnly(completion: string): boolean {
		return /^\s*$/.test(completion)
	}

	/**
	 * Check if completion repeats existing content
	 */
	private isRepeatedContent(completion: string, context: FilterContext): boolean {
		const currentLine = context.linePrefix + context.lineSuffix
		const completionTrimmed = completion.trim()

		// Skip empty completions
		if (completionTrimmed.length === 0) {
			return true
		}

		// Check if completion is just repeating the current line
		if (completionTrimmed === currentLine.trim()) {
			return true
		}

		// Check if completion is just repeating the line prefix
		if (completionTrimmed === context.linePrefix.trim()) {
			return true
		}

		// Check if completion is a substring that already exists in the current line
		if (currentLine.includes(completionTrimmed) && completionTrimmed.length > 3) {
			return true
		}

		// Check for exact repetition of nearby lines
		const currentLineNumber = context.position.line
		const document = context.document

		for (let i = Math.max(0, currentLineNumber - 5); i <= Math.min(document.lineCount - 1, currentLineNumber + 5); i++) {
			if (i === currentLineNumber) continue

			const line = document.lineAt(i).text.trim()
			if (line.length === 0) continue

			// Check for exact match
			if (completionTrimmed === line) {
				return true
			}

			// Check if completion is a significant substring of an existing line
			if (line.includes(completionTrimmed) && completionTrimmed.length > 10) {
				return true
			}

			// Check if completion starts with the same significant content as existing line
			if (completionTrimmed.length > 15 && line.length > 15) {
				const completionStart = completionTrimmed.substring(0, 15)
				const lineStart = line.substring(0, 15)
				if (completionStart === lineStart) {
					return true
				}
			}
		}

		// Check for repetition within the completion itself (e.g., "console.log console.log")
		const words = completionTrimmed.split(/\s+/)
		if (words.length >= 4) {
			const firstHalf = words.slice(0, Math.floor(words.length / 2)).join(" ")
			const secondHalf = words.slice(Math.floor(words.length / 2)).join(" ")
			if (firstHalf === secondHalf && firstHalf.length > 5) {
				return true
			}
		}

		return false
	}

	/**
	 * Get current configuration
	 */
	public getConfig(): CompletionFilterConfig {
		return { ...this.config }
	}
}
