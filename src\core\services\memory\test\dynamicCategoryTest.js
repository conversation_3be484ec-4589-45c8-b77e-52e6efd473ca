const fs = require("fs/promises")
const path = require("path")

// Mock the required modules
const { v4: uuidv4 } = require("uuid")

// Example topic categories for testing
const EXAMPLE_TOPIC_CATEGORIES = {
	"Code Context Extraction": "代码上下文提取相关的偏好和需求",
	"File Management": "文件管理相关的偏好和操作",
	"Memory Service": "内存服务相关的配置和优化",
	"Service Initialization": "服务初始化相关的模式和偏好",
	"User Input Handling": "用户输入处理相关的逻辑和偏好",
	"UI/UX Preferences": "界面和用户体验相关的偏好",
	"Context Menu": "上下文菜单相关的配置和偏好",
	Testing: "测试相关的偏好和方法",
	"Code Style": "代码风格和规范相关的偏好",
	"Development Tools": "开发工具相关的偏好和配置",
}

// Simple MemoryManager mock for testing dynamic categories
class DynamicMemoryManager {
	constructor() {
		this.storage = {} // Dynamic storage - no predefined categories
	}

	// New format generation method with dynamic categories
	generateMarkdown() {
		const lines = []

		Object.entries(this.storage).forEach(([category, entries]) => {
			if (entries.length === 0) {
				return
			}

			// Use the category name directly as the display name
			lines.push(`# ${category}`)

			entries.forEach((entry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		return lines.join("\n")
	}

	// New format parsing method with dynamic categories
	parseMemoriesFromMarkdown(content) {
		// Reset storage
		this.storage = {}

		const lines = content.split("\n")
		let currentCategory = null

		for (const line of lines) {
			// Check for category headers (using # instead of ##)
			const categoryMatch = line.match(/^# (.+)$/)
			if (categoryMatch) {
				currentCategory = categoryMatch[1].trim()
				// Ensure category exists in storage
				if (!this.storage[currentCategory]) {
					this.storage[currentCategory] = []
				}
				continue
			}

			// Check for bullet points
			if (currentCategory && line.startsWith("- ")) {
				const summary = line.slice(2).trim()
				if (summary) {
					const entry = {
						id: uuidv4(),
						category: currentCategory,
						summary,
						originalInput: "",
						timestamp: Date.now(),
						confidence: 1.0,
					}
					this.storage[currentCategory].push(entry)
				}
			}
		}
	}

	// Add entry to dynamic category
	addEntry(category, summary) {
		if (!this.storage[category]) {
			this.storage[category] = []
		}

		const entry = {
			id: uuidv4(),
			category,
			summary,
			originalInput: "",
			timestamp: Date.now(),
			confidence: 0.9,
		}

		this.storage[category].push(entry)
	}

	getAllMemories() {
		return { ...this.storage }
	}

	getMemoriesByCategory(category) {
		return [...(this.storage[category] || [])]
	}
}

class DynamicCategoryTest {
	constructor() {
		this.testDir = path.join(__dirname, "dynamic-test-output")
		this.memoryManager = new DynamicMemoryManager()
	}

	async setup() {
		// Create test directory
		await fs.mkdir(this.testDir, { recursive: true })
	}

	async testDynamicCategories() {
		console.log("🧪 Testing dynamic category system...")

		// Add entries with various dynamic categories
		const testEntries = [
			{
				category: "Code Context Extraction",
				summary:
					"When extracting code context, comments should be included within the context code rather than returned as separate associatedComments field.",
			},
			{
				category: "Code Context Extraction",
				summary:
					"User prefers using tree-sitter for function definition extraction instead of the current implementation approach.",
			},
			{
				category: "Memory Service",
				summary: "For memory service: monitor workspace directory changes.",
			},
			{
				category: "Memory Service",
				summary:
					"User is concerned about the high frequency of getWorkspacePaths gRPC calls and prefers optimized API call performance.",
			},
			{
				category: "Service Initialization",
				summary: "User prefers clean, efficient service initialization patterns that avoid redundant operations.",
			},
			{
				category: "UI/UX Preferences",
				summary: "For WorkspaceHeader-style components: width should match parent input container.",
			},
			{
				category: "Context Menu",
				summary:
					"User prefers organizing Cline commands as submenu items under a main 'Send to Cline' entry in the right-click context menu.",
			},
			{
				category: "Testing",
				summary: "User prefers comprehensive test coverage with both unit and integration tests.",
			},
		]

		// Add entries to memory manager
		for (const entry of testEntries) {
			this.memoryManager.addEntry(entry.category, entry.summary)
		}

		// Generate markdown and check format
		const markdown = this.memoryManager.generateMarkdown()
		console.log("Generated markdown with dynamic categories:")
		console.log("=".repeat(60))
		console.log(markdown)
		console.log("=".repeat(60))

		// Verify the format
		this.verifyDynamicFormat(markdown)

		// Test parsing the new format
		await this.testDynamicParsing(markdown)

		// Test category flexibility
		await this.testCategoryFlexibility()
	}

	verifyDynamicFormat(markdown) {
		const lines = markdown.split("\n")

		// Check for category headers using # instead of ##
		const categoryHeaders = lines.filter((line) => line.match(/^# [^#]/))
		if (categoryHeaders.length === 0) {
			throw new Error("❌ No category headers found")
		}

		// Check that we have the expected dynamic categories
		const expectedCategories = [
			"Code Context Extraction",
			"Memory Service",
			"Service Initialization",
			"UI/UX Preferences",
			"Context Menu",
			"Testing",
		]

		for (const expectedCategory of expectedCategories) {
			const found = categoryHeaders.some((header) => header.includes(expectedCategory))
			if (!found) {
				throw new Error(`❌ Expected category '${expectedCategory}' not found`)
			}
		}

		// Check for bullet points
		const bulletPoints = lines.filter((line) => line.startsWith("- "))
		if (bulletPoints.length === 0) {
			throw new Error("❌ No bullet points found")
		}

		console.log("✅ Dynamic format verification passed")
		console.log(`   - Found ${categoryHeaders.length} dynamic category headers`)
		console.log(`   - Found ${bulletPoints.length} bullet points`)
		console.log(`   - Categories: ${categoryHeaders.map((h) => h.replace("# ", "")).join(", ")}`)
	}

	async testDynamicParsing(markdown) {
		console.log("🧪 Testing parsing of dynamic categories...")

		// Create a new memory manager to test parsing
		const testManager = new DynamicMemoryManager()

		// Parse the markdown
		testManager.parseMemoriesFromMarkdown(markdown)

		// Get all memories and verify
		const allMemories = testManager.getAllMemories()
		let totalEntries = 0

		console.log("📊 Parsed dynamic categories:")
		for (const [category, entries] of Object.entries(allMemories)) {
			if (entries.length > 0) {
				totalEntries += entries.length
				console.log(`   - ${category}: ${entries.length} entries`)
			}
		}

		if (totalEntries === 0) {
			throw new Error("❌ No entries parsed from markdown")
		}

		// Verify specific categories exist
		if (!allMemories["Code Context Extraction"] || allMemories["Code Context Extraction"].length === 0) {
			throw new Error("❌ Code Context Extraction category not parsed correctly")
		}

		if (!allMemories["Memory Service"] || allMemories["Memory Service"].length === 0) {
			throw new Error("❌ Memory Service category not parsed correctly")
		}

		console.log(
			`✅ Dynamic parsing test passed - ${totalEntries} entries across ${Object.keys(allMemories).length} categories`,
		)
	}

	async testCategoryFlexibility() {
		console.log("🧪 Testing category flexibility...")

		// Test adding new categories on the fly
		this.memoryManager.addEntry("API Integration", "User prefers RESTful API design with proper error handling.")
		this.memoryManager.addEntry(
			"Performance Optimization",
			"User prioritizes memory efficiency over execution speed in most cases.",
		)
		this.memoryManager.addEntry("Security", "User requires input validation and sanitization for all user-facing endpoints.")

		const allMemories = this.memoryManager.getAllMemories()
		const categoryCount = Object.keys(allMemories).length

		if (categoryCount < 8) {
			// Should have at least 8 categories now
			throw new Error(`❌ Expected at least 8 categories, got ${categoryCount}`)
		}

		// Test that new categories are properly stored
		const apiEntries = this.memoryManager.getMemoriesByCategory("API Integration")
		if (apiEntries.length === 0) {
			throw new Error("❌ New API Integration category not created properly")
		}

		console.log(`✅ Category flexibility test passed - ${categoryCount} total categories`)
		console.log(`   - New categories: API Integration, Performance Optimization, Security`)
	}

	async cleanup() {
		try {
			await fs.rm(this.testDir, { recursive: true, force: true })
		} catch (error) {
			console.warn("Warning: Could not clean up test directory:", error)
		}
	}

	async run() {
		try {
			await this.setup()
			await this.testDynamicCategories()
			console.log("🎉 All dynamic category tests passed!")
		} catch (error) {
			console.error("❌ Dynamic category test failed:", error.message)
			throw error
		} finally {
			await this.cleanup()
		}
	}
}

// Run the test if this file is executed directly
if (require.main === module) {
	const test = new DynamicCategoryTest()
	test.run().catch(console.error)
}

module.exports = { DynamicCategoryTest }
