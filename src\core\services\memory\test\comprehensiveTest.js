const fs = require("fs/promises")
const path = require("path")

/**
 * Comprehensive test suite for the dynamic memory category system
 */
class ComprehensiveMemoryTest {
	constructor() {
		this.testDir = path.join(__dirname, "comprehensive-test-output")
		this.testResults = {
			passed: 0,
			failed: 0,
			total: 0,
			details: [],
		}
	}

	async setup() {
		await fs.mkdir(this.testDir, { recursive: true })
	}

	async runTest(testName, testFunction) {
		this.testResults.total++
		try {
			console.log(`🧪 Running: ${testName}`)
			await testFunction()
			this.testResults.passed++
			this.testResults.details.push({ name: testName, status: "✅ PASSED" })
			console.log(`   ✅ ${testName} - PASSED`)
		} catch (error) {
			this.testResults.failed++
			this.testResults.details.push({ name: testName, status: "❌ FAILED", error: error.message })
			console.log(`   ❌ ${testName} - FAILED: ${error.message}`)
		}
	}

	async testTypeDefinitions() {
		const typesPath = path.join(__dirname, "..", "types.ts")
		const content = await fs.readFile(typesPath, "utf-8")

		// Test 1: MemoryEntry uses string category
		if (!content.includes("category: string")) {
			throw new Error("MemoryEntry.category should be string type")
		}

		// Test 2: MemoryStorage supports dynamic keys
		if (!content.includes("[category: string]: MemoryEntry[]")) {
			throw new Error("MemoryStorage should support dynamic string keys")
		}

		// Test 3: Example topic categories are defined
		if (!content.includes("EXAMPLE_TOPIC_CATEGORIES")) {
			throw new Error("EXAMPLE_TOPIC_CATEGORIES should be defined")
		}

		// Test 4: At least 10 example categories
		const categoryMatches = content.match(/"[^"]+": "[^"]+"/g)
		if (!categoryMatches || categoryMatches.length < 10) {
			throw new Error("Should have at least 10 example topic categories")
		}
	}

	async testMemoryManagerImplementation() {
		const managerPath = path.join(__dirname, "..", "MemoryManager.ts")
		const content = await fs.readFile(managerPath, "utf-8")

		// Test 1: Storage initialization
		if (!content.includes("return {} as MemoryStorage")) {
			throw new Error("Storage should be initialized as empty object")
		}

		// Test 2: Dynamic category support in generateMarkdown
		if (!content.includes("lines.push(`# ${category}`)")) {
			throw new Error("generateMarkdown should use category name directly")
		}

		// Test 3: Dynamic category parsing
		if (!content.includes("currentCategory = categoryMatch[1].trim()")) {
			throw new Error("parseMemoriesFromMarkdown should parse category names directly")
		}

		// Test 4: Category creation on demand
		if (!content.includes("this.storage[categoryKey] = []")) {
			throw new Error("Should create categories on demand")
		}

		// Test 5: String category parameter
		if (!content.includes("getMemoriesByCategory(category: string)")) {
			throw new Error("getMemoriesByCategory should accept string parameter")
		}
	}

	async testAIPrompts() {
		const promptsPath = path.join(__dirname, "..", "prompts.ts")
		const content = await fs.readFile(promptsPath, "utf-8")

		// Test 1: Dynamic topic classification system
		if (!content.includes("动态主题分类系统")) {
			throw new Error("Should describe dynamic topic classification system")
		}

		// Test 2: Topic identification principles
		if (!content.includes("识别主题") || !content.includes("复用现有主题") || !content.includes("创建新主题")) {
			throw new Error("Should define topic identification principles")
		}

		// Test 3: English naming convention
		if (!content.includes("英文命名") || !content.includes("Pascal Case")) {
			throw new Error("Should specify English naming convention")
		}

		// Test 4: Example topic categories usage
		if (!content.includes("EXAMPLE_TOPIC_CATEGORIES")) {
			throw new Error("Should use EXAMPLE_TOPIC_CATEGORIES in prompts")
		}

		// Test 5: Updated examples with dynamic categories
		if (!content.includes("Code Context Extraction") || !content.includes("Memory Service")) {
			throw new Error("Should include examples with dynamic categories")
		}
	}

	async testMarkdownGeneration() {
		// Mock memory manager for testing
		const mockStorage = {
			"Code Context Extraction": [
				{
					id: "1",
					summary: "Test summary 1",
					category: "Code Context Extraction",
					timestamp: Date.now(),
					confidence: 0.9,
					originalInput: "",
				},
				{
					id: "2",
					summary: "Test summary 2",
					category: "Code Context Extraction",
					timestamp: Date.now(),
					confidence: 0.8,
					originalInput: "",
				},
			],
			"Memory Service": [
				{
					id: "3",
					summary: "Test summary 3",
					category: "Memory Service",
					timestamp: Date.now(),
					confidence: 0.9,
					originalInput: "",
				},
			],
			"New Dynamic Category": [
				{
					id: "4",
					summary: "Test summary 4",
					category: "New Dynamic Category",
					timestamp: Date.now(),
					confidence: 0.7,
					originalInput: "",
				},
			],
		}

		// Simulate markdown generation
		const lines = []
		Object.entries(mockStorage).forEach(([category, entries]) => {
			if (entries.length === 0) return
			lines.push(`# ${category}`)
			entries.forEach((entry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		const markdown = lines.join("\n")

		// Test 1: Correct header format
		if (!markdown.includes("# Code Context Extraction")) {
			throw new Error("Should generate correct category headers")
		}

		// Test 2: No old-style headers
		if (markdown.includes("## ") || markdown.includes("🔧") || markdown.includes("💻")) {
			throw new Error("Should not contain old-style headers or icons")
		}

		// Test 3: Bullet points
		if (!markdown.includes("- Test summary 1")) {
			throw new Error("Should generate bullet points for entries")
		}

		// Test 4: Multiple categories
		const categoryCount = (markdown.match(/^# /gm) || []).length
		if (categoryCount !== 3) {
			throw new Error(`Should generate 3 categories, got ${categoryCount}`)
		}
	}

	async testMarkdownParsing() {
		const testMarkdown = `# Code Context Extraction
- When extracting code context, comments should be included within the context code rather than returned as separate associatedComments field.
- User prefers using tree-sitter for function definition extraction instead of the current implementation approach.

# Memory Service
- For memory service: monitor workspace directory changes.
- User is concerned about the high frequency of getWorkspacePaths gRPC calls and prefers optimized API call performance.

# New Dynamic Category
- This is a test entry for a new dynamic category.

`

		// Simulate parsing logic
		const storage = {}
		const lines = testMarkdown.split("\n")
		let currentCategory = null

		for (const line of lines) {
			const categoryMatch = line.match(/^# (.+)$/)
			if (categoryMatch) {
				currentCategory = categoryMatch[1].trim()
				if (!storage[currentCategory]) {
					storage[currentCategory] = []
				}
				continue
			}

			if (currentCategory && line.startsWith("- ")) {
				const summary = line.slice(2).trim()
				if (summary) {
					storage[currentCategory].push({
						id: `test-${Date.now()}-${Math.random()}`,
						category: currentCategory,
						summary,
						originalInput: "",
						timestamp: Date.now(),
						confidence: 1.0,
					})
				}
			}
		}

		// Test 1: Correct number of categories
		const categoryCount = Object.keys(storage).length
		if (categoryCount !== 3) {
			throw new Error(`Should parse 3 categories, got ${categoryCount}`)
		}

		// Test 2: Correct category names
		if (!storage["Code Context Extraction"] || !storage["Memory Service"] || !storage["New Dynamic Category"]) {
			throw new Error("Should parse all category names correctly")
		}

		// Test 3: Correct number of entries
		const totalEntries = Object.values(storage).reduce((sum, entries) => sum + entries.length, 0)
		if (totalEntries !== 5) {
			throw new Error(`Should parse 5 entries, got ${totalEntries}`)
		}

		// Test 4: Dynamic category creation
		if (!storage["New Dynamic Category"] || storage["New Dynamic Category"].length !== 1) {
			throw new Error("Should create new dynamic categories during parsing")
		}
	}

	async testCategoryFlexibility() {
		// Test dynamic category creation and management
		const mockStorage = {}

		// Test 1: Adding entries to new categories
		const categories = ["API Integration", "Performance Optimization", "Security", "Documentation", "Testing"]

		for (const category of categories) {
			if (!mockStorage[category]) {
				mockStorage[category] = []
			}
			mockStorage[category].push({
				id: `test-${category}`,
				category,
				summary: `Test entry for ${category}`,
				originalInput: "",
				timestamp: Date.now(),
				confidence: 0.8,
			})
		}

		// Test 2: Category count
		if (Object.keys(mockStorage).length !== 5) {
			throw new Error("Should support multiple dynamic categories")
		}

		// Test 3: Category names preservation
		for (const category of categories) {
			if (!mockStorage[category]) {
				throw new Error(`Category ${category} should be preserved`)
			}
		}

		// Test 4: Entry retrieval
		const apiEntries = mockStorage["API Integration"] || []
		if (apiEntries.length !== 1) {
			throw new Error("Should retrieve entries by category")
		}
	}

	async testMemoriesFileFormat() {
		const memoriesPath = path.join(__dirname, "..", "..", "..", "..", "..", ".clinerules", "memories.md")

		try {
			const content = await fs.readFile(memoriesPath, "utf-8")

			// Test 1: No old-style title
			if (content.includes("# Cline Memories")) {
				throw new Error("Should not contain old-style title")
			}

			// Test 2: Dynamic category headers
			const categoryHeaders = content.match(/^# [^#]/gm) || []
			if (categoryHeaders.length === 0) {
				throw new Error("Should contain category headers")
			}

			// Test 3: No icons in headers
			if (content.includes("🔧") || content.includes("💻") || content.includes("📝")) {
				throw new Error("Should not contain icons in headers")
			}

			// Test 4: Bullet points
			const bulletPoints = content.match(/^- /gm) || []
			if (bulletPoints.length === 0) {
				throw new Error("Should contain bullet points")
			}

			// Test 5: Expected categories
			const expectedCategories = ["Code Context Extraction", "Memory Service", "UI/UX Preferences"]
			for (const category of expectedCategories) {
				if (!content.includes(category)) {
					throw new Error(`Should contain category: ${category}`)
				}
			}
		} catch (error) {
			if (error.code === "ENOENT") {
				throw new Error("memories.md file should exist")
			}
			throw error
		}
	}

	async testBackwardCompatibility() {
		// Test that the system can handle mixed old/new format content
		const mixedContent = `# Code Context Extraction
- When extracting code context, comments should be included within the context code.

# Memory Service  
- For memory service: monitor workspace directory changes.

# Legacy Category
- This should work even with non-standard category names.

`

		// Simulate parsing
		const storage = {}
		const lines = mixedContent.split("\n")
		let currentCategory = null

		for (const line of lines) {
			const categoryMatch = line.match(/^# (.+)$/)
			if (categoryMatch) {
				currentCategory = categoryMatch[1].trim()
				if (!storage[currentCategory]) {
					storage[currentCategory] = []
				}
				continue
			}

			if (currentCategory && line.startsWith("- ")) {
				const summary = line.slice(2).trim()
				if (summary) {
					storage[currentCategory].push({ summary, category: currentCategory })
				}
			}
		}

		// Test 1: All categories parsed
		if (Object.keys(storage).length !== 3) {
			throw new Error("Should parse all categories including legacy ones")
		}

		// Test 2: Legacy category support
		if (!storage["Legacy Category"]) {
			throw new Error("Should support legacy category names")
		}
	}

	async cleanup() {
		try {
			await fs.rm(this.testDir, { recursive: true, force: true })
		} catch (error) {
			console.warn("Warning: Could not clean up test directory:", error)
		}
	}

	async run() {
		try {
			await this.setup()

			console.log("🚀 Starting comprehensive memory system tests...\n")

			// Run all tests
			await this.runTest("Type Definitions", () => this.testTypeDefinitions())
			await this.runTest("MemoryManager Implementation", () => this.testMemoryManagerImplementation())
			await this.runTest("AI Prompts", () => this.testAIPrompts())
			await this.runTest("Markdown Generation", () => this.testMarkdownGeneration())
			await this.runTest("Markdown Parsing", () => this.testMarkdownParsing())
			await this.runTest("Category Flexibility", () => this.testCategoryFlexibility())
			await this.runTest("Memories File Format", () => this.testMemoriesFileFormat())
			await this.runTest("Backward Compatibility", () => this.testBackwardCompatibility())

			// Print results
			this.printResults()
		} catch (error) {
			console.error("❌ Test suite failed:", error.message)
			throw error
		} finally {
			await this.cleanup()
		}
	}

	printResults() {
		const coverage = ((this.testResults.passed / this.testResults.total) * 100).toFixed(1)

		console.log("\n" + "=".repeat(60))
		console.log("📊 COMPREHENSIVE TEST RESULTS")
		console.log("=".repeat(60))
		console.log(`Total Tests:     ${this.testResults.total}`)
		console.log(`Passed:          ${this.testResults.passed}`)
		console.log(`Failed:          ${this.testResults.failed}`)
		console.log(`Coverage:        ${coverage}%`)
		console.log("=".repeat(60))

		// Print detailed results
		this.testResults.details.forEach((detail) => {
			console.log(`${detail.status} ${detail.name}`)
			if (detail.error) {
				console.log(`    Error: ${detail.error}`)
			}
		})

		console.log("=".repeat(60))

		if (this.testResults.failed === 0) {
			console.log("🎉 All tests passed! Dynamic memory category system is fully functional.")
		} else {
			console.log(`❌ ${this.testResults.failed} test(s) failed. Please review the issues above.`)
			process.exit(1)
		}
	}
}

// Run comprehensive tests
if (require.main === module) {
	const test = new ComprehensiveMemoryTest()
	test.run().catch(console.error)
}

module.exports = { ComprehensiveMemoryTest }
