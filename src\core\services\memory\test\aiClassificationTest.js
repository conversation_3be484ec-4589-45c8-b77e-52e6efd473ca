const fs = require("fs/promises")
const path = require("path")

/**
 * Test AI classification with dynamic categories
 */
class AIClassificationTest {
	constructor() {
		this.testDir = path.join(__dirname, "ai-classification-test-output")
	}

	async setup() {
		await fs.mkdir(this.testDir, { recursive: true })
	}

	// Mock AI classification function that simulates the new dynamic system
	mockAIClassification(userInput) {
		const classifications = {
			// Code Context Extraction examples
			"当提取代码上下文时，注释应该包含在上下文代码中": {
				category: "Code Context Extraction",
				summary:
					"When extracting code context, comments should be included within the context code rather than returned as separate associatedComments field.",
				confidence: 0.95,
			},
			"我希望使用tree-sitter来提取函数定义": {
				category: "Code Context Extraction",
				summary:
					"User prefers using tree-sitter for function definition extraction instead of the current implementation approach.",
				confidence: 0.9,
			},

			// Memory Service examples
			内存服务应该监控工作区目录变化: {
				category: "Memory Service",
				summary: "For memory service: monitor workspace directory changes.",
				confidence: 0.92,
			},
			我担心getWorkspacePaths调用频率太高: {
				category: "Memory Service",
				summary:
					"User is concerned about the high frequency of getWorkspacePaths gRPC calls and prefers optimized API call performance.",
				confidence: 0.88,
			},

			// Service Initialization examples
			服务初始化应该避免冗余操作: {
				category: "Service Initialization",
				summary: "User prefers clean, efficient service initialization patterns that avoid redundant operations.",
				confidence: 0.9,
			},

			// UI/UX Preferences examples
			WorkspaceHeader组件的宽度应该匹配父容器: {
				category: "UI/UX Preferences",
				summary: "For WorkspaceHeader-style components: width should match parent input container.",
				confidence: 0.85,
			},
			按钮应该有不同的颜色来显示可点击: {
				category: "UI/UX Preferences",
				summary:
					"For WorkspaceHeader-style components: buttons should have slightly different color from input background to show they're clickable.",
				confidence: 0.8,
			},

			// Context Menu examples
			我希望Cline命令组织在子菜单中: {
				category: "Context Menu",
				summary:
					"User prefers organizing Cline commands as submenu items under a main 'Send to Cline' entry in the right-click context menu.",
				confidence: 0.87,
			},

			// Testing examples
			我喜欢全面的测试覆盖: {
				category: "Testing",
				summary: "User prefers comprehensive test coverage with both unit and integration tests.",
				confidence: 0.9,
			},

			// New dynamic categories
			API应该有适当的错误处理: {
				category: "API Design",
				summary: "User prefers APIs with proper error handling and validation.",
				confidence: 0.85,
			},
			性能优化时优先考虑内存效率: {
				category: "Performance Optimization",
				summary: "User prioritizes memory efficiency over execution speed in performance optimization.",
				confidence: 0.82,
			},
			所有用户输入都需要验证和清理: {
				category: "Security",
				summary: "User requires input validation and sanitization for all user-facing endpoints.",
				confidence: 0.9,
			},
			文档应该包含代码示例: {
				category: "Documentation",
				summary: "User prefers documentation that includes practical code examples.",
				confidence: 0.8,
			},
		}

		return (
			classifications[userInput] || {
				category: "General",
				summary: userInput,
				confidence: 0.3,
			}
		)
	}

	async testDynamicCategoryCreation() {
		console.log("🧪 Testing dynamic category creation through AI classification...")

		const testInputs = [
			"当提取代码上下文时，注释应该包含在上下文代码中",
			"内存服务应该监控工作区目录变化",
			"服务初始化应该避免冗余操作",
			"WorkspaceHeader组件的宽度应该匹配父容器",
			"我希望Cline命令组织在子菜单中",
			"我喜欢全面的测试覆盖",
			"API应该有适当的错误处理",
			"性能优化时优先考虑内存效率",
			"所有用户输入都需要验证和清理",
			"文档应该包含代码示例",
		]

		const storage = {}
		const processedEntries = []

		// Simulate AI classification and storage
		for (const input of testInputs) {
			const classification = this.mockAIClassification(input)

			if (classification.confidence >= 0.6) {
				// Create category if it doesn't exist
				if (!storage[classification.category]) {
					storage[classification.category] = []
				}

				const entry = {
					id: `test-${Date.now()}-${Math.random()}`,
					category: classification.category,
					summary: classification.summary,
					originalInput: input,
					timestamp: Date.now(),
					confidence: classification.confidence,
				}

				storage[classification.category].push(entry)
				processedEntries.push(entry)
			}
		}

		// Verify results
		const categoryCount = Object.keys(storage).length
		const totalEntries = processedEntries.length

		console.log(`   ✅ Created ${categoryCount} dynamic categories`)
		console.log(`   ✅ Processed ${totalEntries} entries`)

		// Check for expected categories
		const expectedCategories = [
			"Code Context Extraction",
			"Memory Service",
			"Service Initialization",
			"UI/UX Preferences",
			"Context Menu",
			"Testing",
			"API Design",
			"Performance Optimization",
			"Security",
			"Documentation",
		]

		let foundCategories = 0
		for (const category of expectedCategories) {
			if (storage[category]) {
				foundCategories++
				console.log(`   ✅ Category: ${category} (${storage[category].length} entries)`)
			}
		}

		if (foundCategories < 8) {
			throw new Error(`Expected at least 8 categories, found ${foundCategories}`)
		}

		return storage
	}

	async testCategoryConsistency() {
		console.log("🧪 Testing category consistency...")

		// Test that similar inputs go to the same category
		const relatedInputs = [
			["当提取代码上下文时，注释应该包含在上下文代码中", "我希望使用tree-sitter来提取函数定义"],
			["内存服务应该监控工作区目录变化", "我担心getWorkspacePaths调用频率太高"],
			["WorkspaceHeader组件的宽度应该匹配父容器", "按钮应该有不同的颜色来显示可点击"],
		]

		for (const [input1, input2] of relatedInputs) {
			const classification1 = this.mockAIClassification(input1)
			const classification2 = this.mockAIClassification(input2)

			if (classification1.category !== classification2.category) {
				throw new Error(
					`Related inputs should have same category: "${input1}" -> ${classification1.category}, "${input2}" -> ${classification2.category}`,
				)
			}

			console.log(`   ✅ Consistent categorization: ${classification1.category}`)
		}
	}

	async testMarkdownGeneration() {
		console.log("🧪 Testing markdown generation with dynamic categories...")

		const storage = await this.testDynamicCategoryCreation()

		// Generate markdown
		const lines = []
		Object.entries(storage).forEach(([category, entries]) => {
			if (entries.length === 0) return
			lines.push(`# ${category}`)
			entries.forEach((entry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		const markdown = lines.join("\n")

		// Verify markdown format
		const categoryHeaders = markdown.match(/^# [^#]/gm) || []
		const bulletPoints = markdown.match(/^- /gm) || []

		if (categoryHeaders.length === 0) {
			throw new Error("No category headers found in generated markdown")
		}

		if (bulletPoints.length === 0) {
			throw new Error("No bullet points found in generated markdown")
		}

		// Check for specific categories
		if (!markdown.includes("# Code Context Extraction")) {
			throw new Error("Should include Code Context Extraction category")
		}

		if (!markdown.includes("# API Design")) {
			throw new Error("Should include dynamically created API Design category")
		}

		console.log(`   ✅ Generated ${categoryHeaders.length} category headers`)
		console.log(`   ✅ Generated ${bulletPoints.length} bullet points`)

		// Save test output
		const outputPath = path.join(this.testDir, "test-output.md")
		await fs.writeFile(outputPath, markdown, "utf-8")
		console.log(`   ✅ Saved test output to ${outputPath}`)

		return markdown
	}

	async testCategoryEvolution() {
		console.log("🧪 Testing category evolution over time...")

		const storage = {}

		// Simulate adding entries over time
		const timeSequence = [
			// Initial entries
			{ input: "当提取代码上下文时，注释应该包含在上下文代码中", time: 1 },
			{ input: "内存服务应该监控工作区目录变化", time: 2 },

			// Related entries that should go to existing categories
			{ input: "我希望使用tree-sitter来提取函数定义", time: 3 },
			{ input: "我担心getWorkspacePaths调用频率太高", time: 4 },

			// New category emergence
			{ input: "API应该有适当的错误处理", time: 5 },
			{ input: "性能优化时优先考虑内存效率", time: 6 },

			// Further expansion of existing categories
			{ input: "服务初始化应该避免冗余操作", time: 7 },
			{ input: "所有用户输入都需要验证和清理", time: 8 },
		]

		const categoryEvolution = []

		for (const { input, time } of timeSequence) {
			const classification = this.mockAIClassification(input)

			if (classification.confidence >= 0.6) {
				if (!storage[classification.category]) {
					storage[classification.category] = []
					categoryEvolution.push({
						time,
						action: "NEW_CATEGORY",
						category: classification.category,
						totalCategories: Object.keys(storage).length,
					})
				} else {
					categoryEvolution.push({
						time,
						action: "EXPAND_CATEGORY",
						category: classification.category,
						totalCategories: Object.keys(storage).length,
					})
				}

				storage[classification.category].push({
					id: `evolution-${time}`,
					category: classification.category,
					summary: classification.summary,
					timestamp: time,
					confidence: classification.confidence,
				})
			}
		}

		// Verify evolution pattern
		const newCategoryEvents = categoryEvolution.filter((e) => e.action === "NEW_CATEGORY")
		const expandEvents = categoryEvolution.filter((e) => e.action === "EXPAND_CATEGORY")

		console.log(`   ✅ Created ${newCategoryEvents.length} new categories over time`)
		console.log(`   ✅ Expanded existing categories ${expandEvents.length} times`)

		// Check that we have both new category creation and expansion
		if (newCategoryEvents.length === 0) {
			throw new Error("Should create new categories over time")
		}

		if (expandEvents.length === 0) {
			throw new Error("Should expand existing categories over time")
		}

		return categoryEvolution
	}

	async cleanup() {
		try {
			await fs.rm(this.testDir, { recursive: true, force: true })
		} catch (error) {
			console.warn("Warning: Could not clean up test directory:", error)
		}
	}

	async run() {
		try {
			await this.setup()

			console.log("🚀 Starting AI classification tests for dynamic categories...\n")

			await this.testDynamicCategoryCreation()
			await this.testCategoryConsistency()
			await this.testMarkdownGeneration()
			await this.testCategoryEvolution()

			console.log("\n🎉 All AI classification tests passed!")
			console.log("\n📋 Test Summary:")
			console.log("   ✅ Dynamic category creation through AI classification")
			console.log("   ✅ Category consistency for related inputs")
			console.log("   ✅ Markdown generation with dynamic categories")
			console.log("   ✅ Category evolution and expansion over time")
			console.log("   ✅ Proper handling of new vs existing categories")
		} catch (error) {
			console.error("❌ AI classification test failed:", error.message)
			throw error
		} finally {
			await this.cleanup()
		}
	}
}

// Run AI classification tests
if (require.main === module) {
	const test = new AIClassificationTest()
	test.run().catch(console.error)
}

module.exports = { AIClassificationTest }
