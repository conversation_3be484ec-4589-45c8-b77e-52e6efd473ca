import { EXAMPLE_TOPIC_CATEGORIES, MemoryEntry } from "./types"

/**
 * System prompt for memory classification and summarization with dynamic topic-based categories
 */
export const MEMORY_CLASSIFICATION_SYSTEM_PROMPT = `你是一个专业的用户输入分析助手。你的任务是分析用户在编程助手对话中的输入内容，根据具体主题进行分类并生成简洁的一句话总结。

## 动态主题分类系统

不再使用固定的预设分类，而是根据用户输入的具体内容，识别或创建合适的主题分类。主题应该：

1. **具体且有意义**：反映实际的功能模块、技术领域或开发场景
2. **便于归类**：相似的偏好应该归类到同一主题下
3. **英文命名**：使用英文作为主题名称，便于系统处理
4. **描述性强**：主题名称应该清楚表达其包含的内容类型

## 示例主题分类

以下是一些常见的主题分类示例（不限于这些）：

${Object.entries(EXAMPLE_TOPIC_CATEGORIES)
	.map(([topic, description]) => `- **${topic}**: ${description}`)
	.join("\n")}

## 分类原则

1. **识别主题**：分析用户输入涉及的具体功能、技术或场景
2. **复用现有主题**：如果输入内容与已有主题相关，使用现有主题
3. **创建新主题**：如果是新的领域或功能，创建合适的新主题
4. **保持一致性**：相似的偏好应该使用相同的主题分类

## 分析要求

1. **准确分类**：根据用户输入的主要意图选择或创建最合适的主题
2. **简洁总结**：用一句话（不超过100字）总结用户的核心意图或偏好
3. **置信度评估**：评估分类的准确性（0-1之间的数值）
4. **避免重复**：如果内容过于通用或重复，可以不处理

## 输出格式

请严格按照以下JSON格式输出：
\`\`\`json
{
  "category": "Topic Name",
  "summary": "一句话总结",
  "confidence": 0.85,
  "reasoning": "分类理由（可选）"
}
\`\`\`

## 注意事项

- 只处理有价值的信息，忽略纯粹的问候、感谢等
- 总结要具体且有用，避免过于抽象
- 不需要记录用户的一次性动作，这类内容返回confidence为0
- 如果输入内容不适合记录为memory，返回confidence为0
- 重点是用户的明确偏好和需求表达
- 主题名称使用英文，采用Pascal Case格式（如"Code Context Extraction"）

## 示例

用户输入：
"""
当提取代码上下文时，注释应该包含在上下文代码中，而不是作为单独的associatedComments字段返回
"""

输出：
\`\`\`json
{
  "category": "Code Context Extraction",
  "summary": "When extracting code context, comments should be included within the context code rather than returned as separate associatedComments field.",
  "confidence": 0.95,
  "reasoning": "用户明确表达了对代码上下文提取方式的偏好"
}
\`\`\`

用户输入：
"""
我希望内存服务能够监控工作区目录的变化
"""

输出：
\`\`\`json
{
  "category": "Memory Service",
  "summary": "For memory service: monitor workspace directory changes.",
  "confidence": 0.9,
  "reasoning": "用户对内存服务功能的具体需求"
}
\`\`\`

用户输入：
"""
请帮我查一下天气怎么样
"""

输出：
\`\`\`json
{
  "category": "General",
  "summary": "用户询问天气情况",
  "confidence": 0,
  "reasoning": "与开发无关，不记录为memory"
}
\`\`\`
`

/**
 * Generate user prompt for memory classification
 */
export function generateMemoryClassificationPrompt(userInput: string, context?: string): string {
	let prompt = `请分析以下用户输入并进行分类总结：

用户输入：
"""
${userInput}
"""
`

	if (context) {
		prompt += `
上下文信息：
"""
${context}
"""
`
	}

	prompt += `
请根据系统提示中的分类体系和要求，输出JSON格式的分析结果。`

	return prompt
}

/**
 * Prompt for generating topic-specific insights
 */
export function generateTopicInsightPrompt(topic: string, entries: string[]): string {
	return `基于用户在"${topic}"主题下的历史记录，生成一个简洁的洞察总结：

历史记录：
${entries.map((entry, index) => `${index + 1}. ${entry}`).join("\n")}

请生成一个不超过100字的洞察总结，突出用户在${topic}方面的主要特点和偏好。`
}

/**
 * Prompt for memory deduplication
 */
export function generateDeduplicationPrompt(newEntry: string, existingEntries: string[]): string {
	return `判断新的记录是否与现有记录重复或相似：

新记录：
"${newEntry}"

现有记录：
${existingEntries.map((entry, index) => `${index + 1}. ${entry}`).join("\n")}

请回答：
1. 是否重复或高度相似？（是/否）
2. 如果相似，建议如何合并或更新？

输出格式：
\`\`\`json
{
  "isDuplicate": true/false,
  "similarityScore": 0.85,
  "suggestion": "合并建议或保留理由"
}
\`\`\``
}

/**
 * System prompt for unified memory update analysis
 */
export const UNIFIED_MEMORY_UPDATE_SYSTEM_PROMPT = `你是一个专业的记忆管理助手。你的任务是分析用户的新输入，结合现有的记忆内容，给出统一的更新建议。

## 动态主题分类系统

使用基于主题的动态分类系统，根据具体的功能模块、技术领域或开发场景进行分类，记录用户（开发者）在本次会话中明确表达或反复出现的偏好、约定、注意事项

## 示例主题分类

${Object.entries(EXAMPLE_TOPIC_CATEGORIES)
	.map(([topic, description]) => `- **${topic}**: ${description}`)
	.join("\n")}

## 更新操作类型

1. **ADD**: 添加新的记忆条目
2. **UPDATE**: 更新现有记忆条目的内容
3. **REMOVE**: 删除不再相关的记忆条目
4. **MERGE**: 合并相似的记忆条目

## 分析要求

1. **全面分析**: 同时考虑新输入和现有记忆内容，并考虑用户在本次会话中明确表达或反复出现的偏好、约定、注意事项
2. **避免重复**: 识别并处理重复或相似的内容
3. **保持一致**: 确保记忆内容的逻辑一致性
4. **质量控制**: 只保留有价值的信息，即偏好、约定、注意事项
5. **类型安全**: 所有操作必须符合定义的数据结构

## 输出格式

请严格按照以下JSON格式输出：
\`\`\`json
{
  "shouldUpdate": true/false,
  "instructions": [
    {
      "operation": "add|update|remove|merge",
      "category": "分类名称",
      "targetId": "目标条目ID（更新/删除时需要）",
      "newEntry": {
        "summary": "一句话总结",
        "confidence": 0.85,
        "reasoning": "操作理由"
      },
      "mergeWith": ["条目ID1", "条目ID2"]
    }
  ],
  "reasoning": "整体更新理由",
  "newEntryClassification": {
    "category": "分类名称",
    "summary": "新输入的总结",
    "confidence": 0.85,
    "reasoning": "分类理由"
  }
}
\`\`\`

## 处理原则

- 如果新输入不值得记录，返回 shouldUpdate: false
- 优先合并相似内容而不是创建重复条目
- 更新时保持原有条目的ID和时间戳
- 删除过时或不再相关的信息
- 确保每个分类下的条目数量合理`

/**
 * Generate unified memory update prompt
 */
export function generateUnifiedMemoryUpdatePrompt(
	userInput: string,
	existingMemories: Record<string, MemoryEntry[]>,
	context?: string,
): string {
	let prompt = `请分析用户的新输入，结合现有记忆内容，给出统一的更新建议：

用户新输入：
"""
${userInput}
"""
`

	if (context) {
		prompt += `
上下文信息：
"""
${context}
"""
`
	}

	prompt += `
现有记忆内容：
`

	// 添加现有记忆内容
	Object.entries(existingMemories).forEach(([category, entries]) => {
		if (entries.length > 0) {
			prompt += `
### ${category}
${entries.map((entry, index) => `${index + 1}. [ID: ${entry.id}] ${entry.summary} (置信度: ${entry.confidence})`).join("\n")}
`
		}
	})

	prompt += `
请根据系统提示中的要求和原则，分析新输入与现有记忆的关系，给出具体的更新指令。`

	return prompt
}
