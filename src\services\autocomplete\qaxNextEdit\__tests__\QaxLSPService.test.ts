import * as assert from "assert"
import * as vscode from "vscode"
import { QaxLSPService } from "../services/QaxLSPService"
import { QaxChangeType } from "../types/QaxNextEditTypes"

// Mock VS Code API
const mockCommands = new Map<string, any>()
const mockVscode = {
	commands: {
		executeCommand: async (command: string, ...args: any[]) => {
			return mockCommands.get(command) || null
		}
	},
	languages: {
		onDidChangeDiagnostics: () => ({ dispose: () => {} })
	},
	Uri: {
		file: (path: string) => ({ fsPath: path, scheme: "file" })
	},
	Range: vscode.Range,
	Position: vscode.Position,
	Location: vscode.Location,
	SymbolKind: vscode.SymbolKind
}

// Mock vscode module
Object.assign(vscode, mockVscode)

describe("QaxLSPService", () => {
	let service: QaxLSPService

	beforeEach(() => {
		QaxLSPService.dispose()
		service = QaxLSPService.getInstance()
		mockCommands.clear()
	})

	afterEach(() => {
		service.dispose()
	})

	describe("Service Initialization", () => {
		it("should create a singleton instance", () => {
			const instance1 = QaxLSPService.getInstance()
			const instance2 = QaxLSPService.getInstance()
			assert.strictEqual(instance1, instance2)
		})

		it("should dispose cleanly", () => {
			service.dispose()
			QaxLSPService.dispose()
			// 应该能够重新创建实例
			const newService = QaxLSPService.getInstance()
			assert.ok(newService)
			newService.dispose()
		})
	})

	describe("Document Symbols", () => {
		it("should return empty array when no symbols available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument

			mockCommands.set("vscode.executeDocumentSymbolProvider", null)

			const symbols = await service.getDocumentSymbols(mockDocument)
			assert.strictEqual(symbols.length, 0)
		})

		it("should convert document symbols correctly", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument

			const mockDocumentSymbols = [
				{
					name: "testFunction",
					kind: vscode.SymbolKind.Function,
					selectionRange: new vscode.Range(0, 0, 0, 12),
					range: new vscode.Range(0, 0, 2, 1),
					detail: "function testFunction(): void",
					children: []
				},
				{
					name: "TestClass",
					kind: vscode.SymbolKind.Class,
					selectionRange: new vscode.Range(4, 0, 4, 9),
					range: new vscode.Range(4, 0, 10, 1),
					detail: "class TestClass",
					children: [
						{
							name: "method",
							kind: vscode.SymbolKind.Method,
							selectionRange: new vscode.Range(5, 2, 5, 8),
							range: new vscode.Range(5, 2, 7, 3),
							detail: "method(): string",
							children: []
						}
					]
				}
			]

			mockCommands.set("vscode.executeDocumentSymbolProvider", mockDocumentSymbols)

			const symbols = await service.getDocumentSymbols(mockDocument)
			assert.strictEqual(symbols.length, 3) // 2 top-level + 1 child

			// 检查函数符号
			const functionSymbol = symbols.find(s => s.name === "testFunction")
			assert.ok(functionSymbol)
			assert.strictEqual(functionSymbol.kind, vscode.SymbolKind.Function)
			assert.strictEqual(functionSymbol.detail, "function testFunction(): void")

			// 检查类符号
			const classSymbol = symbols.find(s => s.name === "TestClass")
			assert.ok(classSymbol)
			assert.strictEqual(classSymbol.kind, vscode.SymbolKind.Class)

			// 检查方法符号
			const methodSymbol = symbols.find(s => s.name === "method")
			assert.ok(methodSymbol)
			assert.strictEqual(methodSymbol.kind, vscode.SymbolKind.Method)
			assert.strictEqual(methodSymbol.containerName, "TestClass")
		})

		it("should handle command execution errors gracefully", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument

			mockCommands.set("vscode.executeDocumentSymbolProvider", () => {
				throw new Error("LSP not available")
			})

			const symbols = await service.getDocumentSymbols(mockDocument)
			assert.strictEqual(symbols.length, 0)
		})
	})

	describe("References", () => {
		it("should return empty array when no references available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			mockCommands.set("vscode.executeReferenceProvider", null)

			const references = await service.getReferences(mockDocument, position)
			assert.strictEqual(references.length, 0)
		})

		it("should return references when available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			const mockReferences = [
				new vscode.Location(
					vscode.Uri.file("test.ts"),
					new vscode.Range(0, 4, 0, 12)
				),
				new vscode.Location(
					vscode.Uri.file("test.ts"),
					new vscode.Range(5, 0, 5, 8)
				)
			]

			mockCommands.set("vscode.executeReferenceProvider", mockReferences)

			const references = await service.getReferences(mockDocument, position)
			assert.strictEqual(references.length, 2)
			assert.strictEqual(references[0].uri.fsPath, "test.ts")
		})

		it("should handle reference provider errors gracefully", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			mockCommands.set("vscode.executeReferenceProvider", () => {
				throw new Error("Reference provider failed")
			})

			const references = await service.getReferences(mockDocument, position)
			assert.strictEqual(references.length, 0)
		})
	})

	describe("Definitions", () => {
		it("should return empty array when no definitions available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			mockCommands.set("vscode.executeDefinitionProvider", null)

			const definitions = await service.getDefinitions(mockDocument, position)
			assert.strictEqual(definitions.length, 0)
		})

		it("should return definitions when available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			const mockDefinitions = [
				new vscode.Location(
					vscode.Uri.file("test.ts"),
					new vscode.Range(10, 0, 10, 12)
				)
			]

			mockCommands.set("vscode.executeDefinitionProvider", mockDefinitions)

			const definitions = await service.getDefinitions(mockDocument, position)
			assert.strictEqual(definitions.length, 1)
			assert.strictEqual(definitions[0].uri.fsPath, "test.ts")
		})
	})

	describe("Type Definitions", () => {
		it("should return empty array when no type definitions available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			mockCommands.set("vscode.executeTypeDefinitionProvider", null)

			const typeDefinitions = await service.getTypeDefinitions(mockDocument, position)
			assert.strictEqual(typeDefinitions.length, 0)
		})

		it("should return type definitions when available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			const mockTypeDefinitions = [
				new vscode.Location(
					vscode.Uri.file("types.ts"),
					new vscode.Range(5, 0, 5, 15)
				)
			]

			mockCommands.set("vscode.executeTypeDefinitionProvider", mockTypeDefinitions)

			const typeDefinitions = await service.getTypeDefinitions(mockDocument, position)
			assert.strictEqual(typeDefinitions.length, 1)
			assert.strictEqual(typeDefinitions[0].uri.fsPath, "types.ts")
		})
	})

	describe("Rename Info", () => {
		it("should return null when rename not available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			mockCommands.set("vscode.prepareRename", null)

			const renameInfo = await service.getRenameInfo(mockDocument, position)
			assert.strictEqual(renameInfo, null)
		})

		it("should return rename info when available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const position = new vscode.Position(0, 5)

			const mockRenameInfo = {
				range: new vscode.Range(0, 4, 0, 12),
				placeholder: "testFunction"
			}

			mockCommands.set("vscode.prepareRename", mockRenameInfo)

			const renameInfo = await service.getRenameInfo(mockDocument, position)
			assert.ok(renameInfo)
			assert.strictEqual(renameInfo.placeholder, "testFunction")
		})
	})

	describe("Symbol Rename Detection", () => {
		it("should detect symbol rename when references exist", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const oldPosition = new vscode.Position(0, 5)
			const newPosition = new vscode.Position(0, 8)

			const mockReferences = [
				new vscode.Location(
					vscode.Uri.file("test.ts"),
					new vscode.Range(0, 4, 0, 12)
				)
			]
			const mockDefinitions = [
				new vscode.Location(
					vscode.Uri.file("test.ts"),
					new vscode.Range(10, 0, 10, 12)
				)
			]

			mockCommands.set("vscode.executeReferenceProvider", mockReferences)
			mockCommands.set("vscode.executeDefinitionProvider", mockDefinitions)

			const detection = await service.detectSymbolRename(
				mockDocument,
				oldPosition,
				newPosition,
				"oldName",
				"newName"
			)

			assert.ok(detection)
			assert.strictEqual(detection.type, QaxChangeType.VARIABLE_RENAME)
			assert.strictEqual(detection.oldValue, "oldName")
			assert.strictEqual(detection.newValue, "newName")
			assert.strictEqual(detection.confidence, 0.9)
			assert.ok(detection.metadata?.references)
			assert.ok(detection.metadata?.definitions)
		})

		it("should return null when no references or definitions exist", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument
			const oldPosition = new vscode.Position(0, 5)
			const newPosition = new vscode.Position(0, 8)

			mockCommands.set("vscode.executeReferenceProvider", [])
			mockCommands.set("vscode.executeDefinitionProvider", [])

			const detection = await service.detectSymbolRename(
				mockDocument,
				oldPosition,
				newPosition,
				"oldName",
				"newName"
			)

			assert.strictEqual(detection, null)
		})
	})

	describe("LSP Availability", () => {
		it("should return true when LSP is available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument

			mockCommands.set("vscode.executeDocumentSymbolProvider", [])

			const isAvailable = await service.isLSPAvailable(mockDocument)
			assert.strictEqual(isAvailable, true)
		})

		it("should return false when LSP is not available", async () => {
			const mockDocument = {
				uri: { fsPath: "test.ts" }
			} as vscode.TextDocument

			mockCommands.set("vscode.executeDocumentSymbolProvider", () => {
				throw new Error("LSP not available")
			})

			const isAvailable = await service.isLSPAvailable(mockDocument)
			assert.strictEqual(isAvailable, false)
		})
	})
})
