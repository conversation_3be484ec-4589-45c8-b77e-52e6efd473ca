#!/usr/bin/env node

/**
 * Comprehensive test runner for the Todo List System
 *
 * This script tests all aspects of the todo list functionality:
 * - Core parsing and validation
 * - Tool execution and integration
 * - UI synchronization
 * - Session persistence
 * - Error handling
 */

const { execSync } = require("child_process")
const path = require("path")
const fs = require("fs")

console.log("🚀 Starting Todo List System Tests...\n")

// Test configuration
const testFiles = ["src/test/todo-system.test.ts", "src/test/todo-integration.test.ts"]

const testResults = {
	passed: 0,
	failed: 0,
	total: 0,
	details: [],
}

// Helper function to run tests
function runTest(testFile) {
	console.log(`📋 Running ${testFile}...`)

	try {
		const result = execSync(`npx vitest run ${testFile}`, {
			encoding: "utf8",
			cwd: process.cwd(),
		})

		console.log(`✅ ${testFile} - PASSED`)
		testResults.passed++
		testResults.details.push({
			file: testFile,
			status: "PASSED",
			output: result,
		})
	} catch (error) {
		console.log(`❌ ${testFile} - FAILED`)
		console.log(error.stdout || error.message)
		testResults.failed++
		testResults.details.push({
			file: testFile,
			status: "FAILED",
			error: error.stdout || error.message,
		})
	}

	testResults.total++
	console.log("")
}

// Manual integration tests
function runManualTests() {
	console.log("🔧 Running Manual Integration Tests...\n")

	// Test 1: Markdown parsing accuracy
	console.log("Test 1: Markdown Parsing Accuracy")
	try {
		const { parseMarkdownChecklist, todoListToMarkdown } = require("../src/core/tools/updateTodoListTool.js")

		const testMarkdown = `
- [ ] Create comprehensive todo system
- [x] Implement core parsing logic
- [-] Add UI synchronization
- [ ] Write documentation
        `.trim()

		const parsed = parseMarkdownChecklist(testMarkdown)
		const regenerated = todoListToMarkdown(parsed)

		console.log("Original markdown:")
		console.log(testMarkdown)
		console.log("\nParsed todos:", parsed.length)
		console.log("Regenerated markdown:")
		console.log(regenerated)

		if (parsed.length === 4) {
			console.log("✅ Parsing test PASSED - Correct number of todos parsed")
		} else {
			console.log("❌ Parsing test FAILED - Incorrect number of todos")
			testResults.failed++
		}

		testResults.total++
		console.log("")
	} catch (error) {
		console.log("❌ Manual parsing test FAILED:", error.message)
		testResults.failed++
		testResults.total++
	}

	// Test 2: Status normalization
	console.log("Test 2: Status Normalization")
	try {
		const { normalizeStatus } = require("../src/core/tools/updateTodoListTool.js")

		const testCases = [
			{ input: "pending", expected: "pending" },
			{ input: "completed", expected: "completed" },
			{ input: "in_progress", expected: "in_progress" },
			{ input: "invalid", expected: "pending" },
			{ input: "", expected: "pending" },
		]

		let allPassed = true
		testCases.forEach(({ input, expected }) => {
			const result = normalizeStatus(input)
			if (result !== expected) {
				console.log(`❌ Status normalization failed: ${input} -> ${result} (expected ${expected})`)
				allPassed = false
			}
		})

		if (allPassed) {
			console.log("✅ Status normalization test PASSED")
			testResults.passed++
		} else {
			console.log("❌ Status normalization test FAILED")
			testResults.failed++
		}

		testResults.total++
		console.log("")
	} catch (error) {
		console.log("❌ Manual status normalization test FAILED:", error.message)
		testResults.failed++
		testResults.total++
	}

	// Test 3: Reminder formatting
	console.log("Test 3: Reminder Formatting")
	try {
		const { formatReminderSection } = require("../src/core/environment/reminder.js")

		const testTodos = [
			{ id: "1", content: "Test task 1", status: "pending" },
			{ id: "2", content: "Test task 2", status: "completed" },
			{ id: "3", content: "Test task 3", status: "in_progress" },
		]

		const reminder = formatReminderSection(testTodos)

		console.log("Generated reminder section:")
		console.log(reminder)

		if (reminder.includes("REMINDERS") && reminder.includes("Test task 1")) {
			console.log("✅ Reminder formatting test PASSED")
			testResults.passed++
		} else {
			console.log("❌ Reminder formatting test FAILED")
			testResults.failed++
		}

		testResults.total++
		console.log("")
	} catch (error) {
		console.log("❌ Manual reminder formatting test FAILED:", error.message)
		testResults.failed++
		testResults.total++
	}
}

// Run all tests
function runAllTests() {
	// Check if test files exist
	const missingFiles = testFiles.filter((file) => !fs.existsSync(file))
	if (missingFiles.length > 0) {
		console.log("⚠️  Warning: Some test files are missing:")
		missingFiles.forEach((file) => console.log(`   - ${file}`))
		console.log("")
	}

	// Run unit tests
	testFiles.forEach((testFile) => {
		if (fs.existsSync(testFile)) {
			runTest(testFile)
		}
	})

	// Run manual integration tests
	runManualTests()

	// Print summary
	console.log("📊 Test Summary")
	console.log("================")
	console.log(`Total tests: ${testResults.total}`)
	console.log(`Passed: ${testResults.passed}`)
	console.log(`Failed: ${testResults.failed}`)
	console.log(`Success rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`)

	if (testResults.failed > 0) {
		console.log("\n❌ Some tests failed. Check the output above for details.")
		process.exit(1)
	} else {
		console.log("\n✅ All tests passed! Todo list system is ready for use.")
		process.exit(0)
	}
}

// Check if we're in the right directory
if (!fs.existsSync("package.json")) {
	console.log("❌ Error: Please run this script from the project root directory")
	process.exit(1)
}

// Run the tests
runAllTests()
