// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Extension",
			"type": "extensionHost",
			"request": "launch",
			"args": ["--extensionDevelopmentPath=${workspaceFolder}", "--disable-workspace-trust", "${workspaceFolder}"],
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"preLaunchTask": "${defaultBuildTask}",
			"env": {
				"IS_DEV": "true",
				"QAX_ENVIRONMENT": "development",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}",
				"VSCODE_EXTENSION_SUPPORT_NODE_GLOBAL_NAVIGATOR": "false"
			}
		},
		{
			"name": "Run Extension (Fresh Install Mode)",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--user-data-dir=${workspaceFolder}/dist/tmp/user",
				"--profile-temp",
				"--sync=off",
				"--disable-extensions",
				"--extensionDevelopmentPath=${workspaceFolder}",
				"${workspaceFolder}"
			],
			"outFiles": ["${workspaceFolder}/dist/**/*.js"],
			"preLaunchTask": "clean-tmp-user",
			"internalConsoleOptions": "openOnSessionStart",
			"postDebugTask": "stop",
			"env": {
				"IS_DEV": "true",
				"TEMP_PROFILE": "true",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}"
			}
		},
		{
			"type": "node",
			"request": "launch",
			"name": "Run Standalone Service",
			"skipFiles": ["<node_internals>/**"],
			"sourceMaps": true,
			"resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"],
			"cwd": "${workspaceFolder}/dist-standalone",
			"outFiles": ["${workspaceFolder}/dist-standalone/**/*.js"],
			"preLaunchTask": "compile-standalone",
			"env": {
				// Turns on grpc debug log.
				//"GRPC_TRACE": "all",
				//"GRPC_VERBOSITY": "DEBUG",
				"NODE_PATH": "${workspaceFolder}/dist-standalone/node_modules",

				"HOST_BRIDGE_ADDRESS": "localhost:50052"
			},
			"program": "standalone.js"
		}
	]
}
