"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QaxLSPService = void 0;
const vscode = __importStar(require("vscode"));
const QaxNextEditTypes_1 = require("../types/QaxNextEditTypes");
/**
 * LSP 集成服务，用于获取语言服务器提供的符号信息、引用、定义等
 */
class QaxLSPService {
    constructor() {
        this.disposables = [];
        this.setupEventHandlers();
    }
    static getInstance() {
        if (!QaxLSPService.instance) {
            QaxLSPService.instance = new QaxLSPService();
        }
        return QaxLSPService.instance;
    }
    static dispose() {
        if (QaxLSPService.instance) {
            QaxLSPService.instance.dispose();
            QaxLSPService.instance = null;
        }
    }
    setupEventHandlers() {
        // 监听语言服务器状态变化
        this.disposables.push(vscode.languages.onDidChangeDiagnostics(() => {
            // 可以在这里处理诊断信息变化
        }));
    }
    /**
     * 获取文档中的所有符号
     */
    async getDocumentSymbols(document) {
        try {
            const symbols = await vscode.commands.executeCommand("vscode.executeDocumentSymbolProvider", document.uri);
            if (!symbols) {
                return [];
            }
            return this.convertDocumentSymbols(symbols, document.uri);
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to get document symbols:", error);
            return [];
        }
    }
    /**
     * 获取符号的所有引用
     */
    async getReferences(document, position) {
        try {
            const references = await vscode.commands.executeCommand("vscode.executeReferenceProvider", document.uri, position);
            return references || [];
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to get references:", error);
            return [];
        }
    }
    /**
     * 获取符号的定义
     */
    async getDefinitions(document, position) {
        try {
            const definitions = await vscode.commands.executeCommand("vscode.executeDefinitionProvider", document.uri, position);
            return definitions || [];
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to get definitions:", error);
            return [];
        }
    }
    /**
     * 获取符号的类型定义
     */
    async getTypeDefinitions(document, position) {
        try {
            const typeDefinitions = await vscode.commands.executeCommand("vscode.executeTypeDefinitionProvider", document.uri, position);
            return typeDefinitions || [];
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to get type definitions:", error);
            return [];
        }
    }
    /**
     * 获取重命名信息
     */
    async getRenameInfo(document, position) {
        try {
            const renameInfo = await vscode.commands.executeCommand("vscode.prepareRename", document.uri, position);
            return renameInfo || null;
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to get rename info:", error);
            return null;
        }
    }
    /**
     * 执行重命名预览
     */
    async previewRename(document, position, newName) {
        try {
            const workspaceEdit = await vscode.commands.executeCommand("vscode.executeDocumentRenameProvider", document.uri, position, newName);
            return workspaceEdit || null;
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to preview rename:", error);
            return null;
        }
    }
    /**
     * 获取悬停信息
     */
    async getHoverInfo(document, position) {
        try {
            const hover = await vscode.commands.executeCommand("vscode.executeHoverProvider", document.uri, position);
            return hover && hover.length > 0 ? hover[0] : null;
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to get hover info:", error);
            return null;
        }
    }
    /**
     * 检测符号是否被重命名
     */
    async detectSymbolRename(document, oldPosition, newPosition, oldText, newText) {
        try {
            // 获取旧位置的符号信息
            const oldReferences = await this.getReferences(document, oldPosition);
            const oldDefinitions = await this.getDefinitions(document, oldPosition);
            // 检查是否是符号重命名
            if (oldReferences.length > 0 || oldDefinitions.length > 0) {
                return {
                    type: QaxNextEditTypes_1.QaxChangeType.VARIABLE_RENAME,
                    filePath: document.uri.fsPath,
                    range: new vscode.Range(oldPosition, newPosition),
                    oldValue: oldText,
                    newValue: newText,
                    confidence: 0.9,
                    metadata: {
                        symbolName: oldText,
                        references: oldReferences,
                        definitions: oldDefinitions
                    }
                };
            }
            return null;
        }
        catch (error) {
            console.warn("QaxLSPService: Failed to detect symbol rename:", error);
            return null;
        }
    }
    /**
     * 转换 DocumentSymbol 为 QaxSymbolInfo
     */
    convertDocumentSymbols(symbols, uri) {
        const result = [];
        const convertSymbol = (symbol, containerName) => {
            const symbolInfo = {
                name: symbol.name,
                kind: symbol.kind,
                location: new vscode.Location(uri, symbol.selectionRange),
                containerName,
                detail: symbol.detail
            };
            result.push(symbolInfo);
            // 递归处理子符号
            if (symbol.children) {
                for (const child of symbol.children) {
                    convertSymbol(child, symbol.name);
                }
            }
        };
        for (const symbol of symbols) {
            convertSymbol(symbol);
        }
        return result;
    }
    /**
     * 检查 LSP 是否可用
     */
    async isLSPAvailable(document) {
        try {
            // 尝试获取文档符号来检查 LSP 是否可用
            const symbols = await vscode.commands.executeCommand("vscode.executeDocumentSymbolProvider", document.uri);
            return symbols !== undefined;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * 清理资源
     */
    dispose() {
        this.disposables.forEach(d => d.dispose());
        this.disposables = [];
    }
}
exports.QaxLSPService = QaxLSPService;
QaxLSPService.instance = null;
