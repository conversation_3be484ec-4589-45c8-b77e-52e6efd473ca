import * as vscode from "vscode"

/**
 * Task execution states for autocomplete management
 */
export enum TaskExecutionState {
	/** No task is running */
	IDLE = "idle",
	/** Task is actively executing */
	RUNNING = "running",
	/** Task is waiting for user interaction (ask messages) */
	WAITING_FOR_USER = "waiting_for_user",
}

/**
 * Manages autocomplete state during task execution
 * Uses a temporary disable flag instead of modifying user configuration
 * Supports intermediate states for user interaction
 */
export class AutocompleteTaskManager {
	private static instance: AutocompleteTaskManager | null = null
	private taskState: TaskExecutionState = TaskExecutionState.IDLE
	private statusBarUpdateCallback: (() => void) | null = null

	private constructor() {}

	public static getInstance(): AutocompleteTaskManager {
		if (!AutocompleteTaskManager.instance) {
			AutocompleteTaskManager.instance = new AutocompleteTaskManager()
		}
		return AutocompleteTaskManager.instance
	}

	/**
	 * Called when a task starts running
	 * Sets state to RUNNING and disables autocomplete
	 */
	public onTaskStart(): void {
		if (this.taskState !== TaskExecutionState.IDLE) {
			return // Task already in progress
		}

		this.taskState = TaskExecutionState.RUNNING
		console.log("🚀🔒 Autocomplete temporarily disabled due to task execution")

		// Update status bar if callback is set
		if (this.statusBarUpdateCallback) {
			this.statusBarUpdateCallback()
		}
	}

	/**
	 * Called when a task stops running completely
	 * Sets state to IDLE and enables autocomplete
	 */
	public onTaskStop(): void {
		if (this.taskState === TaskExecutionState.IDLE) {
			return // No task was running
		}

		this.taskState = TaskExecutionState.IDLE
		console.log("🚀🔓 Autocomplete temporary disable cleared - task completed")

		// Update status bar if callback is set
		if (this.statusBarUpdateCallback) {
			this.statusBarUpdateCallback()
		}
	}

	/**
	 * Called when task enters waiting state (ask messages)
	 * Temporarily enables autocomplete while waiting for user input
	 */
	public onTaskWaitingForUser(): void {
		if (this.taskState !== TaskExecutionState.RUNNING) {
			return // Only transition from RUNNING state
		}

		this.taskState = TaskExecutionState.WAITING_FOR_USER
		console.log("🚀🔓 Autocomplete temporarily enabled - waiting for user interaction")

		// Update status bar if callback is set
		if (this.statusBarUpdateCallback) {
			this.statusBarUpdateCallback()
		}
	}

	/**
	 * Called when task resumes from waiting state
	 * Disables autocomplete as task continues execution
	 */
	public onTaskResumeFromWaiting(): void {
		if (this.taskState !== TaskExecutionState.WAITING_FOR_USER) {
			return // Only transition from WAITING_FOR_USER state
		}

		this.taskState = TaskExecutionState.RUNNING
		console.log("🚀🔒 Autocomplete temporarily disabled - task resumed execution")

		// Update status bar if callback is set
		if (this.statusBarUpdateCallback) {
			this.statusBarUpdateCallback()
		}
	}

	/**
	 * Get the current task execution state
	 */
	public getTaskState(): TaskExecutionState {
		return this.taskState
	}

	/**
	 * Get the current task running state (for backward compatibility)
	 */
	public getIsTaskRunning(): boolean {
		return this.taskState !== TaskExecutionState.IDLE
	}

	/**
	 * Check if autocomplete is temporarily disabled
	 * Only disabled when task is actively RUNNING, not when waiting for user
	 */
	public isTemporarilyDisabled(): boolean {
		return this.taskState === TaskExecutionState.RUNNING
	}

	/**
	 * Check if task is waiting for user interaction
	 */
	public isWaitingForUser(): boolean {
		return this.taskState === TaskExecutionState.WAITING_FOR_USER
	}

	/**
	 * Set status bar update callback
	 */
	public setStatusBarUpdateCallback(callback: () => void): void {
		this.statusBarUpdateCallback = callback
	}

	/**
	 * Force reset the state (for error recovery)
	 */
	public forceReset(): void {
		this.taskState = TaskExecutionState.IDLE
		console.log("🚀🔄 Autocomplete temporary disable state force reset")

		// Update status bar if callback is set
		if (this.statusBarUpdateCallback) {
			this.statusBarUpdateCallback()
		}
	}
}
