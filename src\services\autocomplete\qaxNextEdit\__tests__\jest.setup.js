// Jest setup file for QaxNextEdit tests

// Mock console methods to reduce noise in tests
global.console = {
	...console,
	log: jest.fn(),
	warn: jest.fn(),
	error: jest.fn(),
	info: jest.fn(),
	debug: jest.fn()
}

// Global test utilities
global.createMockDocument = (content, languageId = 'typescript', filePath = 'test.ts') => ({
	uri: { fsPath: filePath },
	languageId,
	getText: (range) => {
		if (!range) return content
		// Simple mock implementation for range-based getText
		const lines = content.split('\n')
		const startLine = Math.min(range.start.line, lines.length - 1)
		const endLine = Math.min(range.end.line, lines.length - 1)
		
		if (startLine === endLine) {
			const line = lines[startLine] || ''
			return line.substring(range.start.character, range.end.character)
		}
		
		let result = ''
		for (let i = startLine; i <= endLine; i++) {
			const line = lines[i] || ''
			if (i === startLine) {
				result += line.substring(range.start.character)
			} else if (i === endLine) {
				result += '\n' + line.substring(0, range.end.character)
			} else {
				result += '\n' + line
			}
		}
		return result
	},
	offsetAt: (position) => {
		const lines = content.split('\n')
		let offset = 0
		for (let i = 0; i < position.line && i < lines.length; i++) {
			offset += lines[i].length + 1 // +1 for newline
		}
		return offset + position.character
	}
})

global.createMockRange = (startLine, startChar, endLine, endChar) => ({
	start: { line: startLine, character: startChar },
	end: { line: endLine, character: endChar },
	isEmpty: startLine === endLine && startChar === endChar,
	isSingleLine: startLine === endLine,
	isEqual: function(other) {
		return this.start.line === other.start.line &&
			   this.start.character === other.start.character &&
			   this.end.line === other.end.line &&
			   this.end.character === other.end.character
	},
	intersection: function(other) {
		// Simple intersection check
		const startLine = Math.max(this.start.line, other.start.line)
		const endLine = Math.min(this.end.line, other.end.line)
		
		if (startLine > endLine) return null
		
		if (startLine === endLine) {
			const startChar = startLine === this.start.line ? 
				Math.max(this.start.character, other.start.character) : 
				Math.max(this.start.character, other.start.character)
			const endChar = endLine === this.end.line ? 
				Math.min(this.end.character, other.end.character) : 
				Math.min(this.end.character, other.end.character)
			
			if (startChar >= endChar) return null
			
			return createMockRange(startLine, startChar, endLine, endChar)
		}
		
		return createMockRange(startLine, 0, endLine, 0)
	}
})

global.createMockLocation = (filePath, range) => ({
	uri: { fsPath: filePath, scheme: 'file' },
	range
})

// Mock timers for debounce testing
jest.useFakeTimers()

// Cleanup after each test
afterEach(() => {
	jest.clearAllMocks()
	jest.clearAllTimers()
})

// Restore real timers after all tests
afterAll(() => {
	jest.useRealTimers()
})
