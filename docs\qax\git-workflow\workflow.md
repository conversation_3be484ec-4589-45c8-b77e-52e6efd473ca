# Git 工作流程文档

## 概述

本文档描述了 Codegen 项目的 Git 分支管理和合并策略，确保 Cline 版本升级和 QAX 功能开发的有序进行。

## 分支结构

```
upstream/tags (Cline官方标签)
    ↓
master (本地Cline同步)
    ↓
codegen (集成分支)
    ↓
feature/* (功能开发分支)
```

### 分支说明

- **upstream/tags**: Cline 官方发布的稳定版本标签
- **master**: 本地同步 Cline 官方稳定版本的分支
- **codegen**: 集成分支，包含 Cline 升级 + QAX 功能
- **feature/***: 基于 codegen 的功能开发分支

## 工作流程

### Cline 版本升级流程
1. 获取 Cline 最新稳定版本标签
2. 创建 cline 分支（基于最新标签）并且推送到 origin
3. 切换到 master 分支并且更新 master，比较上次版本和本次版本的差异，生成更新文档 提交
4. 清理上游删除或者重命名的文件
5. 使用 squash merge 将更改合并到 master, 并且推送到 origin
6. 验证 master 分支状态
7. 切换到 codegen 分支并且更新到最新版本
8. 使用  git merge master 合并， 升级 cline 版本

## 最佳实践

### 1. 分支命名规范

- **功能分支**: `feature/功能名称`
- **修复分支**: `fix/问题描述`

### 2. 提交信息规范

```
feat: 添加新功能
fix: 修复问题
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建/工具相关
upgrade: 版本升级
merge: 分支合并
```

### 3. 文档维护

每次重要的合并操作都应该在 `docs/qax/git-workflow/` 中创建对应的文档：

```
docs/qax/git-wrokflow/
├── README.md              # 合并操作总览
├── workflow.md            # 本工作流程文档
├── v3.18.7-to-v3.18.12.md # 具体版本升级记录
└── feature-xxx-merge.md   # 功能合并记录
```
