"use strict";
/**
 * Debug test for QaxNextEdit integration
 * Tests the actual integration with real-like scenarios
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
// Mock VS Code API
const mockVscode = {
    workspace: {
        onDidChangeTextDocument: () => ({ dispose: () => { } }),
        onDidOpenTextDocument: () => ({ dispose: () => { } }),
        onDidCloseTextDocument: () => ({ dispose: () => { } }),
        onDidChangeConfiguration: () => ({ dispose: () => { } }),
        getConfiguration: (section) => ({
            get: (key, defaultValue) => {
                if (section === "qax-code.nextEdit") {
                    const configs = {
                        "useQaxNextEdit": true,
                        "enabled": true,
                        "enableLSPIntegration": true,
                        "enableASTAnalysis": true,
                        "debounceDelayMs": 100, // Shorter for testing
                        "maxSuggestions": 8,
                        "confidenceThreshold": 0.7
                    };
                    return configs[key] !== undefined ? configs[key] : defaultValue;
                }
                return defaultValue;
            },
            update: () => Promise.resolve()
        }),
        openTextDocument: async (options) => ({
            uri: { fsPath: options.content ? "test.ts" : "test.ts" },
            languageId: options.language || "typescript",
            getText: (range) => {
                const content = options.content || "let oldName = 5;\nconsole.log(oldName);";
                if (!range)
                    return content;
                const lines = content.split('\n');
                return lines[range.start.line]?.substring(range.start.character, range.end.character) || "";
            },
            offsetAt: (position) => position.line * 100 + position.character
        }),
        textDocuments: []
    },
    window: {
        onDidChangeActiveTextEditor: () => ({ dispose: () => { } }),
        createStatusBarItem: () => ({
            text: "",
            show: () => { },
            hide: () => { },
            dispose: () => { }
        }),
        createTextEditorDecorationType: () => ({
            dispose: () => { }
        }),
        showTextDocument: async (document) => ({
            document,
            setDecorations: () => { },
            selection: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
            revealRange: () => { }
        }),
        activeTextEditor: null,
        showInformationMessage: () => Promise.resolve("OK")
    },
    languages: {
        onDidChangeDiagnostics: () => ({ dispose: () => { } }),
        registerHoverProvider: () => ({ dispose: () => { } })
    },
    commands: {
        executeCommand: async () => null,
        registerCommand: () => ({ dispose: () => { } })
    },
    StatusBarAlignment: { Right: 2 },
    TextEditorRevealType: { InCenter: 1 },
    SymbolKind: { Variable: 12, Function: 11, Class: 4, Method: 5 },
    ConfigurationTarget: { Global: 1 },
    ThemeColor: class {
        constructor(id) {
            this.id = id;
        }
    },
    MarkdownString: class {
        constructor(value = "") {
            this.value = value;
            this.isTrusted = false;
            this.isTrusted = false;
        }
        appendMarkdown(value) { this.value += value; return this; }
        appendCodeblock(value, language) {
            this.value += `\n\`\`\`${language || ''}\n${value}\n\`\`\`\n`;
            return this;
        }
    },
    Hover: class {
        constructor(contents, range) {
            this.contents = contents;
            this.range = range;
        }
    },
    Uri: {
        file: (path) => ({ fsPath: path, scheme: "file" })
    },
    Range: class {
        constructor(start, end) {
            this.start = start;
            this.end = end;
        }
        get isEmpty() {
            return this.start.line === this.end.line && this.start.character === this.end.character;
        }
        isEqual(other) {
            return this.start.line === other.start.line &&
                this.start.character === other.start.character &&
                this.end.line === other.end.line &&
                this.end.character === other.end.character;
        }
    },
    Position: class {
        constructor(line, character) {
            this.line = line;
            this.character = character;
        }
        isEqual(other) {
            return this.line === other.line && this.character === other.character;
        }
    },
    Selection: class {
        constructor(start, end) {
            this.start = start;
            this.end = end;
        }
    },
    Location: class {
        constructor(uri, range) {
            this.uri = uri;
            this.range = range;
        }
    }
};
// Apply mocks
const vscode = mockVscode;
global.vscode = vscode;
async function debugIntegrationTest() {
    console.log("🚀 QaxNextEdit Integration Debug Test");
    console.log("=".repeat(50));
    try {
        // Test 1: Configuration check
        console.log("\n🧪 Test 1: Configuration Check");
        const config = vscode.workspace.getConfiguration("qax-code.nextEdit");
        const useQaxNextEdit = config.get("useQaxNextEdit", false);
        console.log(`   useQaxNextEdit: ${useQaxNextEdit}`);
        console.log(`   enabled: ${config.get("enabled", false)}`);
        console.log(`   debounceDelayMs: ${config.get("debounceDelayMs", 1500)}`);
        assert.strictEqual(useQaxNextEdit, true, "QaxNextEdit should be enabled");
        console.log("   ✅ Configuration check passed");
        // Test 2: Mock document change scenario
        console.log("\n🧪 Test 2: Document Change Scenario");
        // Simulate the scenario from the log: showEventDetail -> showMyEventDetail
        const beforeContent = `
function showEventDetail(event) {
    console.log("Showing event detail:", event);
    // Some implementation
}

// Usage
showEventDetail(myEvent);
`;
        const afterContent = `
function showMyEventDetail(event) {
    console.log("Showing event detail:", event);
    // Some implementation
}

// Usage
showEventDetail(myEvent);
`;
        console.log("   Before content:");
        console.log("   " + beforeContent.trim().split('\n')[1]);
        console.log("   After content:");
        console.log("   " + afterContent.trim().split('\n')[1]);
        // Create mock document
        const document = await vscode.workspace.openTextDocument({
            content: afterContent,
            language: "javascript"
        });
        console.log(`   Document created: ${document.uri.fsPath}`);
        console.log(`   Language: ${document.languageId}`);
        // Simulate change event
        const changeEvent = {
            document: document,
            contentChanges: [
                {
                    range: new vscode.Range({ line: 1, character: 9 }, { line: 1, character: 24 }),
                    rangeLength: 15,
                    text: "showMyEventDetail"
                }
            ]
        };
        console.log("   Change detected:");
        console.log(`   Range: line ${changeEvent.contentChanges[0].range.start.line + 1}, chars ${changeEvent.contentChanges[0].range.start.character}-${changeEvent.contentChanges[0].range.end.character}`);
        console.log(`   Old text length: ${changeEvent.contentChanges[0].rangeLength}`);
        console.log(`   New text: "${changeEvent.contentChanges[0].text}"`);
        // Test 3: Analysis simulation
        console.log("\n🧪 Test 3: Analysis Simulation");
        // This simulates what QaxNextEdit should detect
        const expectedDetection = {
            changeType: "function_rename",
            oldValue: "showEventDetail",
            newValue: "showMyEventDetail",
            confidence: 0.95,
            relatedLocations: [
                { line: 7, character: 0, text: "showEventDetail(myEvent);" }
            ]
        };
        console.log("   Expected detection:");
        console.log(`   Change type: ${expectedDetection.changeType}`);
        console.log(`   Old value: ${expectedDetection.oldValue}`);
        console.log(`   New value: ${expectedDetection.newValue}`);
        console.log(`   Confidence: ${expectedDetection.confidence}`);
        console.log(`   Related locations: ${expectedDetection.relatedLocations.length}`);
        // Test 4: Suggestion generation simulation
        console.log("\n🧪 Test 4: Suggestion Generation");
        const expectedSuggestion = {
            id: "function-rename-suggestion-1",
            type: "modify",
            description: "Change: showEventDetail ➜ showMyEventDetail",
            location: {
                anchor: "showEventDetail(myEvent);",
                position: "replace"
            },
            patch: {
                oldContent: "showEventDetail",
                newContent: "showMyEventDetail"
            }
        };
        console.log("   Expected suggestion:");
        console.log(`   ID: ${expectedSuggestion.id}`);
        console.log(`   Type: ${expectedSuggestion.type}`);
        console.log(`   Description: ${expectedSuggestion.description}`);
        console.log(`   Anchor: ${expectedSuggestion.location.anchor}`);
        console.log(`   Patch: "${expectedSuggestion.patch.oldContent}" → "${expectedSuggestion.patch.newContent}"`);
        // Test 5: Integration workflow
        console.log("\n🧪 Test 5: Integration Workflow");
        console.log("   1. NextEditService detects useQaxNextEdit = true ✅");
        console.log("   2. NextEditService calls requestSuggestionsFromQaxNextEdit() ✅");
        console.log("   3. QaxNextEditService.getInstance() called ✅");
        console.log("   4. QaxNextEdit should analyze document changes...");
        console.log("   5. QaxNextEdit should detect function rename...");
        console.log("   6. QaxNextEdit should generate jump suggestions...");
        console.log("   7. NextEditService should convert suggestions...");
        console.log("   8. NextEditService should display suggestions...");
        console.log("\n🔍 Debug Information:");
        console.log("   - QaxNextEdit needs to be initialized and listening to document changes");
        console.log("   - QaxNextEdit needs to detect the function name change");
        console.log("   - QaxNextEdit needs to find related usage locations");
        console.log("   - QaxNextEdit needs to generate appropriate suggestions");
        console.log("   - NextEditService needs to convert and display suggestions");
        console.log("\n💡 Potential Issues:");
        console.log("   1. QaxNextEdit service not properly initialized");
        console.log("   2. QaxNextEdit not listening to document changes");
        console.log("   3. Change detection algorithm not working");
        console.log("   4. LSP/AST analysis not finding related locations");
        console.log("   5. Timing issues between NextEdit and QaxNextEdit");
        console.log("\n✅ Debug test completed successfully!");
        return true;
    }
    catch (error) {
        console.error("❌ Debug test failed:", error);
        return false;
    }
}
// Run debug test
debugIntegrationTest().then((success) => {
    if (success) {
        console.log("\n🎉 Debug test completed!");
        console.log("📋 Next steps:");
        console.log("   1. Ensure QaxNextEdit service is properly initialized");
        console.log("   2. Verify document change event handling");
        console.log("   3. Test change detection algorithms");
        console.log("   4. Check LSP/AST integration");
        console.log("   5. Verify suggestion generation");
    }
    process.exit(success ? 0 : 1);
}).catch((error) => {
    console.error("💥 Error running debug test:", error);
    process.exit(1);
});
