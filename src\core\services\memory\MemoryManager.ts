import * as fs from "fs/promises"
import * as path from "path"
import { v4 as uuidv4 } from "uuid"
import { Anthropic } from "@anthropic-ai/sdk"
import {
	MemoryEntry,
	MemoryStorage,
	MemoryConfig,
	MemoryClassificationResult,
	MemoryUpdateResult,
	Memory<PERSON><PERSON>dateOperation,
	MemoryUpdateInstruction,
	DEFAULT_MEMORY_CONFIG,
} from "./types"
import {
	MEMORY_CLASSIFICATION_SYSTEM_PROMPT,
	generateMemoryClassificationPrompt,
	generateDeduplicationPrompt,
	UNIFIED_MEMORY_UPDATE_SYSTEM_PROMPT,
	generateUnifiedMemoryUpdatePrompt,
} from "./prompts"
import { <PERSON>pi<PERSON>and<PERSON> } from "@api/index"
import { Logger } from "@services/logging/Logger"

/**
 * Memory Manager for handling user input classification and storage
 */
export class MemoryManager {
	private config: MemoryConfig
	private memoriesFilePath: string
	private storage: MemoryStorage
	private processingQueue: Map<string, NodeJS.Timeout> = new Map()
	private apiHandler: ApiHandler
	private isDisposed = false

	constructor(workspaceRoot: string, apiHandler: ApiHand<PERSON>, config: Partial<MemoryConfig> = {}) {
		this.config = { ...DEFAULT_MEMORY_CONFIG, ...config }
		this.memoriesFilePath = path.join(workspaceRoot, ".clinerules", "memories.md")
		this.apiHandler = apiHandler
		this.storage = this.initializeStorage()
		this.loadMemories()
	}

	/**
	 * Initialize empty storage structure for dynamic categories
	 */
	private initializeStorage(): MemoryStorage {
		return {} as MemoryStorage
	}

	/**
	 * Process user input for memory extraction
	 */
	public async processUserInput(input: string, context?: string): Promise<void> {
		if (!this.config.enabled || input.length < this.config.minInputLength) {
			return
		}

		// Debounce processing to avoid excessive API calls
		const inputId = this.generateInputId(input)

		// Clear existing timeout for this input
		const existingTimeout = this.processingQueue.get(inputId)
		if (existingTimeout) {
			clearTimeout(existingTimeout)
		}

		// Set new timeout
		const timeout = setTimeout(async () => {
			try {
				await this.analyzeAndStoreInput(input, context)
				this.processingQueue.delete(inputId)
			} catch (error) {
				Logger.error("Failed to process memory input:", error)
				this.processingQueue.delete(inputId)
			}
		}, this.config.debounceMs)

		this.processingQueue.set(inputId, timeout)
	}

	/**
	 * Process user input with unified memory update (one AI call)
	 */
	public async processUserInputUnified(input: string, context?: string): Promise<void> {
		if (!this.config.enabled || input.length < this.config.minInputLength) {
			return
		}

		// Debounce processing to avoid excessive API calls
		const inputId = this.generateInputId(input)

		// Clear existing timeout for this input
		const existingTimeout = this.processingQueue.get(inputId)
		if (existingTimeout) {
			clearTimeout(existingTimeout)
		}

		// Set new timeout
		const timeout = setTimeout(async () => {
			try {
				await this.analyzeAndUpdateUnified(input, context)
				this.processingQueue.delete(inputId)
			} catch (error) {
				Logger.error("Failed to process unified memory input:", error)
				this.processingQueue.delete(inputId)
			}
		}, this.config.debounceMs)

		this.processingQueue.set(inputId, timeout)
	}

	/**
	 * Analyze input and store if valuable
	 */
	private async analyzeAndStoreInput(input: string, context?: string): Promise<void> {
		try {
			const classification = await this.classifyInput(input, context)

			if (classification.confidence < this.config.confidenceThreshold) {
				Logger.info(`Memory classification confidence too low: ${classification.confidence}`)
				return
			}

			// Check for duplicates
			const existingEntries = this.storage[classification.category]
			const isDuplicate = await this.checkDuplicate(classification.summary, existingEntries)

			if (isDuplicate) {
				Logger.info("Memory entry is duplicate, skipping")
				return
			}

			// Create and store memory entry
			const memoryEntry: MemoryEntry = {
				id: uuidv4(),
				category: classification.category,
				summary: classification.summary,
				originalInput: input,
				timestamp: Date.now(),
				confidence: classification.confidence,
			}

			await this.addMemoryEntry(memoryEntry)
			console.log(`🧠 Memory entry stored successfully: ${classification.category} - ${classification.summary}`)
			Logger.info(`Stored memory entry: ${classification.category} - ${classification.summary}`)
		} catch (error) {
			console.error(`🧠 Failed to analyze and store input: ${error.message}`)
			Logger.error("Failed to analyze and store input:", error)
		}
	}

	/**
	 * Unified analyze and update method (one AI call)
	 */
	private async analyzeAndUpdateUnified(input: string, context?: string): Promise<void> {
		try {
			// Get current memories in the format expected by the prompt
			const existingMemories: Record<string, MemoryEntry[]> = {}
			Object.entries(this.storage).forEach(([category, entries]) => {
				existingMemories[category] = entries
			})

			const updateResult = await this.getUnifiedUpdateInstructions(input, existingMemories, context)

			if (!updateResult.shouldUpdate) {
				console.log("🧠 No memory update needed according to AI analysis")
				return
			}

			// Apply the update instructions
			await this.applyUpdateInstructions(updateResult.instructions, input)
			console.log(`🧠 Successfully applied ${updateResult.instructions.length} memory update instructions`)
			Logger.info(`Applied ${updateResult.instructions.length} memory update instructions`)
		} catch (error) {
			console.error(`🧠 Failed to analyze and update unified: ${error.message}`)
			Logger.error("Failed to analyze and update unified:", error)
		}
	}

	/**
	 * Classify user input using AI with timeout protection
	 */
	private async classifyInput(input: string, context?: string): Promise<MemoryClassificationResult> {
		const userPrompt = generateMemoryClassificationPrompt(input, context)
		const messages: Anthropic.Messages.MessageParam[] = [{ role: "user", content: userPrompt }]

		// Create timeout promise with cleanup
		let timeoutId: NodeJS.Timeout | null = null
		const timeoutPromise = new Promise<never>((_, reject) => {
			timeoutId = setTimeout(() => {
				reject(new Error("AI classification API call timed out"))
			}, 15000) // 15 second timeout
		})

		// Create API call promise
		const apiCallPromise = this.performApiCall(messages)

		// Race between API call and timeout
		try {
			const result = await Promise.race([apiCallPromise, timeoutPromise])
			// Clear timeout if API call succeeded
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
			console.log(`🧠 AI classification successful: ${result.category} (confidence: ${result.confidence})`)
			return result
		} catch (error) {
			// Clear timeout on error as well
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
			console.error(`🧠 AI classification failed: ${error.message}`)
			// Return a default result to prevent blocking
			return {
				category: "General",
				summary: input.substring(0, 100),
				confidence: 0.1,
			}
		}
	}

	/**
	 * Get unified update instructions using AI with timeout protection
	 */
	private async getUnifiedUpdateInstructions(
		input: string,
		existingMemories: Record<string, MemoryEntry[]>,
		context?: string,
	): Promise<MemoryUpdateResult> {
		const userPrompt = generateUnifiedMemoryUpdatePrompt(input, existingMemories, context)

		// Create messages with system prompt as part of user message
		const messages: Anthropic.Messages.MessageParam[] = [
			{
				role: "user",
				content: `${userPrompt}`,
			},
		]

		// Create timeout promise with cleanup
		let timeoutId: NodeJS.Timeout | null = null
		const timeoutPromise = new Promise<never>((_, reject) => {
			timeoutId = setTimeout(() => {
				reject(new Error("Unified memory update API call timed out"))
			}, 60000) // 30 second timeout to allow more time
		})

		// Create API call promise
		const apiCallPromise = this.performUnifiedApiCall(messages)

		// Race between API call and timeout
		try {
			const result = await Promise.race([apiCallPromise, timeoutPromise])
			// Clear timeout if API call succeeded
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
			console.log(
				`🧠 AI unified analysis successful: shouldUpdate=${result.shouldUpdate}, instructions=${result.instructions.length}`,
			)
			return result
		} catch (error) {
			// Clear timeout on error as well
			if (timeoutId) {
				clearTimeout(timeoutId)
			}
			console.error(`🧠 AI unified analysis failed: ${error.message}`)
			// Return a default result to prevent blocking
			return {
				shouldUpdate: false,
				instructions: [],
				reasoning: `AI analysis failed: ${error.message}`,
			}
		}
	}

	/**
	 * Perform the actual API call
	 */
	private async performApiCall(messages: Anthropic.Messages.MessageParam[]): Promise<MemoryClassificationResult> {
		const stream = this.apiHandler.createMessage(MEMORY_CLASSIFICATION_SYSTEM_PROMPT, messages)

		let content = ""
		// Read stream without internal timeout (outer timeout will handle this)
		for await (const chunk of stream) {
			if (chunk.type === "text") {
				content += chunk.text
			}
		}

		// Parse JSON response
		const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)

		if (!jsonMatch) {
			console.error("🧠 Invalid AI classification response format. Content:", content.substring(0, 500))
			throw new Error("Invalid response format from AI classification")
		}

		try {
			const result = JSON.parse(jsonMatch[1]) as MemoryClassificationResult

			// Category validation is now more flexible for dynamic categories
			if (!result.category || typeof result.category !== "string") {
				console.log(`🧠 Invalid category '${result.category}', defaulting to General`)
				result.category = "General"
			}

			return result
		} catch (parseError) {
			console.error("🧠 Failed to parse AI classification JSON:", jsonMatch[1])
			throw new Error(`Failed to parse AI classification response: ${parseError.message}`)
		}
	}

	/**
	 * Perform the unified API call for memory updates
	 */
	private async performUnifiedApiCall(messages: Anthropic.Messages.MessageParam[]): Promise<MemoryUpdateResult> {
		const stream = this.apiHandler.createMessage(UNIFIED_MEMORY_UPDATE_SYSTEM_PROMPT, messages)

		let content = ""
		// Read stream without internal timeout (outer timeout will handle this)
		for await (const chunk of stream) {
			if (chunk.type === "text") {
				content += chunk.text
			}
		}

		// Parse JSON response
		const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)

		if (!jsonMatch) {
			console.error("🧠 Invalid AI unified response format. Content:", content.substring(0, 500))
			throw new Error("Invalid response format from unified AI analysis")
		}

		try {
			const result = JSON.parse(jsonMatch[1]) as MemoryUpdateResult

			// Validate the result structure
			if (typeof result.shouldUpdate !== "boolean") {
				console.error("🧠 Invalid shouldUpdate field in AI response:", result.shouldUpdate)
				throw new Error("Invalid unified response structure: missing shouldUpdate")
			}

			if (!Array.isArray(result.instructions)) {
				console.error("🧠 Invalid instructions field in AI response:", result.instructions)
				throw new Error("Invalid unified response structure: missing instructions array")
			}

			// Validate each instruction
			result.instructions.forEach((instruction, index) => {
				if (!Object.values(MemoryUpdateOperation).includes(instruction.operation)) {
					console.error(`🧠 Invalid operation in instruction ${index}:`, instruction.operation)
					throw new Error(`Invalid operation in instruction ${index}: ${instruction.operation}`)
				}
				if (!instruction.category || typeof instruction.category !== "string") {
					console.error(`🧠 Invalid category in instruction ${index}:`, instruction.category)
					throw new Error(`Invalid category in instruction ${index}: ${instruction.category}`)
				}
			})

			return result
		} catch (parseError) {
			console.error("🧠 Failed to parse AI unified JSON:", jsonMatch[1])
			throw new Error(`Failed to parse AI unified response: ${parseError.message}`)
		}
	}

	/**
	 * Apply update instructions to memory storage
	 */
	private async applyUpdateInstructions(instructions: MemoryUpdateInstruction[], originalInput: string): Promise<void> {
		for (let i = 0; i < instructions.length; i++) {
			const instruction = instructions[i]
			try {
				switch (instruction.operation) {
					case MemoryUpdateOperation.ADD:
						await this.applyAddInstruction(instruction, originalInput)
						console.log(`🧠 ADD operation completed: ${instruction.newEntry?.summary}`)
						break
					case MemoryUpdateOperation.UPDATE:
						await this.applyUpdateInstruction(instruction, originalInput)
						console.log(`🧠 UPDATE operation completed: ${instruction.targetId}`)
						break
					case MemoryUpdateOperation.REMOVE:
						await this.applyRemoveInstruction(instruction)
						console.log(`🧠 REMOVE operation completed: ${instruction.targetId}`)
						break
					case MemoryUpdateOperation.MERGE:
						await this.applyMergeInstruction(instruction, originalInput)
						console.log(`🧠 MERGE operation completed: ${instruction.mergeWith?.length} entries merged`)
						break
				}
			} catch (error) {
				console.error(`🧠 Failed to apply ${instruction.operation} instruction:`, error.message)
				Logger.error("Failed to apply memory update instruction:", error)
				// Continue with other instructions even if one fails
			}
		}

		// Save changes to file
		try {
			await this.saveMemories()
			console.log("🧠 Memory changes saved to file successfully")
		} catch (error) {
			console.error(`🧠 Failed to save memories to file: ${error.message}`)
			Logger.error("Failed to save memories:", error)
			throw error
		}
	}

	/**
	 * Apply ADD instruction
	 */
	private async applyAddInstruction(instruction: MemoryUpdateInstruction, originalInput: string): Promise<void> {
		if (!instruction.newEntry) {
			return
		}

		const memoryEntry: MemoryEntry = {
			id: uuidv4(),
			category: instruction.category,
			summary: instruction.newEntry.summary,
			originalInput: originalInput,
			timestamp: Date.now(),
			confidence: instruction.newEntry.confidence,
		}

		// Ensure category exists in storage
		const categoryKey = instruction.category
		if (!this.storage[categoryKey]) {
			this.storage[categoryKey] = []
		}

		const categoryEntries = this.storage[categoryKey]
		categoryEntries.unshift(memoryEntry)

		// Limit entries per category
		if (categoryEntries.length > this.config.maxEntriesPerCategory) {
			categoryEntries.splice(this.config.maxEntriesPerCategory)
		}
	}

	/**
	 * Apply UPDATE instruction
	 */
	private async applyUpdateInstruction(instruction: MemoryUpdateInstruction, originalInput: string): Promise<void> {
		if (!instruction.targetId || !instruction.newEntry) {
			return
		}

		const categoryEntries = this.storage[instruction.category] || []
		const entryIndex = categoryEntries.findIndex((entry: MemoryEntry) => entry.id === instruction.targetId)

		if (entryIndex === -1) {
			return
		}

		// Update the entry while preserving id and timestamp
		const existingEntry = categoryEntries[entryIndex]
		categoryEntries[entryIndex] = {
			...existingEntry,
			summary: instruction.newEntry.summary,
			confidence: instruction.newEntry.confidence,
			originalInput: originalInput, // Update with new input
		}
	}

	/**
	 * Apply REMOVE instruction
	 */
	private async applyRemoveInstruction(instruction: MemoryUpdateInstruction): Promise<void> {
		if (!instruction.targetId) {
			return
		}

		const categoryEntries = this.storage[instruction.category] || []
		const entryIndex = categoryEntries.findIndex((entry: MemoryEntry) => entry.id === instruction.targetId)

		if (entryIndex === -1) {
			return
		}

		categoryEntries.splice(entryIndex, 1)
	}

	/**
	 * Apply MERGE instruction
	 */
	private async applyMergeInstruction(instruction: MemoryUpdateInstruction, originalInput: string): Promise<void> {
		if (!instruction.mergeWith || !Array.isArray(instruction.mergeWith) || !instruction.newEntry) {
			return
		}

		const categoryEntries = this.storage[instruction.category] || []

		// Find and remove entries to merge
		const entriesToMerge: MemoryEntry[] = []
		for (const targetId of instruction.mergeWith) {
			const entryIndex = categoryEntries.findIndex((entry: MemoryEntry) => entry.id === targetId)
			if (entryIndex !== -1) {
				entriesToMerge.push(categoryEntries.splice(entryIndex, 1)[0])
			}
		}

		if (entriesToMerge.length === 0) {
			return
		}

		// Create new merged entry
		const mergedEntry: MemoryEntry = {
			id: uuidv4(),
			category: instruction.category,
			summary: instruction.newEntry.summary,
			originalInput: originalInput,
			timestamp: Date.now(),
			confidence: instruction.newEntry.confidence,
		}

		categoryEntries.unshift(mergedEntry)
	}

	/**
	 * Check if entry is duplicate
	 */
	private async checkDuplicate(newSummary: string, existingEntries: MemoryEntry[]): Promise<boolean> {
		if (existingEntries.length === 0) {
			return false
		}

		const existingSummaries = existingEntries.map((entry) => entry.summary)
		const prompt = generateDeduplicationPrompt(newSummary, existingSummaries)

		try {
			const messages: Anthropic.Messages.MessageParam[] = [{ role: "user", content: prompt }]

			const stream = this.apiHandler.createMessage("", messages)

			let content = ""
			for await (const chunk of stream) {
				if (chunk.type === "text") {
					content += chunk.text
				}
			}

			const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)
			if (jsonMatch) {
				const result = JSON.parse(jsonMatch[1])
				return result.isDuplicate === true
			}
		} catch (error) {
			Logger.error("Failed to check duplicate:", error)
		}

		return false
	}

	/**
	 * Add memory entry to storage
	 */
	private async addMemoryEntry(entry: MemoryEntry): Promise<void> {
		const categoryEntries = this.storage[entry.category]

		// Add new entry
		categoryEntries.unshift(entry)

		// Limit entries per category
		if (categoryEntries.length > this.config.maxEntriesPerCategory) {
			categoryEntries.splice(this.config.maxEntriesPerCategory)
		}

		// Save to file
		await this.saveMemories()
	}

	/**
	 * Generate unique ID for input (for debouncing)
	 */
	private generateInputId(input: string): string {
		return Buffer.from(input).toString("base64").slice(0, 16)
	}

	/**
	 * Load memories from file
	 */
	private async loadMemories(): Promise<void> {
		try {
			const content = await fs.readFile(this.memoriesFilePath, "utf-8")
			this.parseMemoriesFromMarkdown(content)
		} catch (error) {
			// File doesn't exist, start with empty storage
			Logger.info("Memories file not found, starting with empty storage")
		}
	}

	/**
	 * Save memories to markdown file with timeout protection
	 */
	private async saveMemories(): Promise<void> {
		const markdown = this.generateMarkdown()

		// Ensure directory exists with timeout
		const dir = path.dirname(this.memoriesFilePath)

		try {
			// Add timeout for directory creation
			const dirPromise = fs.mkdir(dir, { recursive: true })
			const dirTimeout = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("Directory creation timeout")), 5000)
			})

			await Promise.race([dirPromise, dirTimeout])
		} catch (error) {
			console.error(`🧠 Failed to create memories directory: ${error.message}`)
			throw error
		}

		try {
			// Add timeout for file writing
			const writePromise = fs.writeFile(this.memoriesFilePath, markdown, "utf-8")
			const writeTimeout = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("File write timeout")), 5000)
			})

			await Promise.race([writePromise, writeTimeout])
		} catch (error) {
			console.error(`🧠 Failed to write memories file: ${error.message}`)
			throw error
		}
	}

	/**
	 * Generate markdown content from storage
	 */
	private generateMarkdown(): string {
		const lines: string[] = []

		Object.entries(this.storage).forEach(([category, entries]) => {
			if (entries.length === 0) {
				return
			}

			// Use the category name directly as the display name
			lines.push(`# ${category}`)

			entries.forEach((entry: MemoryEntry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		return lines.join("\n")
	}

	/**
	 * Parse memories from markdown content
	 */
	private parseMemoriesFromMarkdown(content: string): void {
		// Simple parsing - look for category sections and bullet points
		const lines = content.split("\n")
		let currentCategory: string | null = null

		for (const line of lines) {
			// Check for category headers (now using # instead of ##)
			const categoryMatch = line.match(/^# (.+)$/)
			if (categoryMatch) {
				currentCategory = categoryMatch[1].trim()
				// Ensure category exists in storage
				if (!this.storage[currentCategory]) {
					this.storage[currentCategory] = []
				}
				continue
			}

			// Check for bullet points
			if (currentCategory && line.startsWith("- ")) {
				const summary = line.slice(2).trim()
				if (summary) {
					const entry: MemoryEntry = {
						id: uuidv4(),
						category: currentCategory,
						summary,
						originalInput: "", // Not stored in markdown
						timestamp: Date.now(),
						confidence: 1.0, // Assume high confidence for existing entries
					}
					this.storage[currentCategory].push(entry)
				}
			}
		}
	}

	/**
	 * Get all memories for a category
	 */
	public getMemoriesByCategory(category: string): MemoryEntry[] {
		return [...(this.storage[category] || [])]
	}

	/**
	 * Get all memories
	 */
	public getAllMemories(): MemoryStorage {
		return { ...this.storage }
	}

	/**
	 * Clear all memories
	 */
	public async clearMemories(): Promise<void> {
		this.storage = this.initializeStorage()
		await this.saveMemories()
	}

	/**
	 * Update configuration
	 */
	public updateConfig(newConfig: Partial<MemoryConfig>): void {
		this.config = { ...this.config, ...newConfig }
	}

	/**
	 * Cleanup resources
	 */
	public dispose(): void {
		// Clear all pending timeouts
		this.processingQueue.forEach((timeout) => clearTimeout(timeout))
		this.processingQueue.clear()
	}
}
