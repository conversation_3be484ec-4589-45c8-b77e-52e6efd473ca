import { expect } from "chai"
import { AutocompleteTaskManager, TaskExecutionState } from "../AutocompleteTaskManager"

describe("AutocompleteTaskManager", () => {
	let taskManager: AutocompleteTaskManager
	let statusBarUpdateCallCount: number
	let mockStatusBarUpdate: () => void

	beforeEach(() => {
		// Reset singleton instance for each test
		;(AutocompleteTaskManager as any).instance = null
		taskManager = AutocompleteTaskManager.getInstance()

		// Set up mock status bar update callback
		statusBarUpdateCallCount = 0
		mockStatusBarUpdate = () => {
			statusBarUpdateCallCount++
		}
		taskManager.setStatusBarUpdateCallback(mockStatusBarUpdate)
	})

	afterEach(() => {
		// Clean up
		taskManager.forceReset()
	})

	describe("Task lifecycle management", () => {
		it("should start with autocomplete enabled", () => {
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.getIsTaskRunning()).to.equal(false)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
			expect(taskManager.isWaitingForUser()).to.equal(false)
		})

		it("should disable autocomplete when task starts", () => {
			taskManager.onTaskStart()

			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.getIsTaskRunning()).to.equal(true)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)
			expect(taskManager.isWaitingForUser()).to.equal(false)
			expect(statusBarUpdateCallCount).to.equal(1)
		})

		it("should restore autocomplete when task stops", () => {
			// Start task first
			taskManager.onTaskStart()
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// Stop task
			taskManager.onTaskStop()

			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.getIsTaskRunning()).to.equal(false)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
			expect(statusBarUpdateCallCount).to.equal(2) // Once for start, once for stop
		})

		it("should enable autocomplete when waiting for user", () => {
			// Start task first
			taskManager.onTaskStart()
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// Enter waiting state
			taskManager.onTaskWaitingForUser()

			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.getIsTaskRunning()).to.equal(true) // Still running, just waiting
			expect(taskManager.isTemporarilyDisabled()).to.equal(false) // But not disabled
			expect(taskManager.isWaitingForUser()).to.equal(true)
			expect(statusBarUpdateCallCount).to.equal(2) // Once for start, once for waiting
		})

		it("should disable autocomplete when resuming from waiting", () => {
			// Start task and enter waiting state
			taskManager.onTaskStart()
			taskManager.onTaskWaitingForUser()
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)

			// Resume from waiting
			taskManager.onTaskResumeFromWaiting()

			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)
			expect(taskManager.isWaitingForUser()).to.equal(false)
			expect(statusBarUpdateCallCount).to.equal(3) // Start, waiting, resume
		})

		it("should not start task if already in progress", () => {
			taskManager.onTaskStart()
			const firstCallCount = statusBarUpdateCallCount

			// Try to start again
			taskManager.onTaskStart()

			expect(statusBarUpdateCallCount).to.equal(firstCallCount) // Should not increment
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
		})

		it("should not stop task if not running", () => {
			// Try to stop without starting
			taskManager.onTaskStop()

			expect(statusBarUpdateCallCount).to.equal(0) // Should not call update
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
		})

		it("should handle invalid state transitions gracefully", () => {
			// Try to wait without running
			taskManager.onTaskWaitingForUser()
			expect(statusBarUpdateCallCount).to.equal(0)
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)

			// Try to resume without waiting
			taskManager.onTaskResumeFromWaiting()
			expect(statusBarUpdateCallCount).to.equal(0)
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
		})

		it("should handle task resumption correctly", () => {
			// Simulate task resumption scenario
			taskManager.onTaskStart() // Task resumes and starts running
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// Then enters waiting state for user input
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)

			// User responds and task continues
			taskManager.onTaskResumeFromWaiting()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// Task completes
			taskManager.onTaskStop()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
		})

		it("should handle command approval workflow correctly", () => {
			// Start task
			taskManager.onTaskStart()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)

			// Task asks for command approval
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)

			// User approves command - should immediately disable autocomplete
			taskManager.onTaskResumeFromWaiting()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// Command executes and task continues...
			// Eventually task completes
			taskManager.onTaskStop()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
		})

		it("should handle complete command execution lifecycle", () => {
			// 1. Task starts
			taskManager.onTaskStart()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// 2. Command approval request
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)

			// 3. User approves command
			taskManager.onTaskResumeFromWaiting()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// 4. Command execution completes - state should remain RUNNING
			// (This is handled by the tool execution completion handlers)
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.RUNNING)
			expect(taskManager.isTemporarilyDisabled()).to.equal(true)

			// 5. Task fully completes
			taskManager.onTaskStop()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
		})
	})

	describe("Force reset functionality", () => {
		it("should reset all state when force reset is called", () => {
			// Start task and enter waiting state
			taskManager.onTaskStart()
			taskManager.onTaskWaitingForUser()
			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.WAITING_FOR_USER)

			// Force reset
			taskManager.forceReset()

			expect(taskManager.getTaskState()).to.equal(TaskExecutionState.IDLE)
			expect(taskManager.getIsTaskRunning()).to.equal(false)
			expect(taskManager.isTemporarilyDisabled()).to.equal(false)
			expect(taskManager.isWaitingForUser()).to.equal(false)
			expect(statusBarUpdateCallCount).to.equal(3) // Start, waiting, reset
		})
	})

	describe("Status bar callback management", () => {
		it("should call status bar update callback on all state changes", () => {
			expect(statusBarUpdateCallCount).to.equal(0)

			taskManager.onTaskStart()
			expect(statusBarUpdateCallCount).to.equal(1)

			taskManager.onTaskWaitingForUser()
			expect(statusBarUpdateCallCount).to.equal(2)

			taskManager.onTaskResumeFromWaiting()
			expect(statusBarUpdateCallCount).to.equal(3)

			taskManager.onTaskStop()
			expect(statusBarUpdateCallCount).to.equal(4)

			taskManager.forceReset()
			expect(statusBarUpdateCallCount).to.equal(5)
		})

		it("should work without status bar callback", () => {
			// Remove callback
			taskManager.setStatusBarUpdateCallback(() => {})

			// Should not throw errors
			expect(() => {
				taskManager.onTaskStart()
				taskManager.onTaskWaitingForUser()
				taskManager.onTaskResumeFromWaiting()
				taskManager.onTaskStop()
				taskManager.forceReset()
			}).to.not.throw()
		})
	})

	describe("Singleton pattern", () => {
		it("should return the same instance", () => {
			const instance1 = AutocompleteTaskManager.getInstance()
			const instance2 = AutocompleteTaskManager.getInstance()

			expect(instance1).to.equal(instance2)
		})
	})
})
