#!/usr/bin/env node

/**
 * Validation script for Todo List System implementation
 *
 * This script validates that all the required files and components
 * are properly implemented and integrated.
 */

const fs = require("fs")
const path = require("path")

console.log("🔍 Validating Todo List System Implementation...\n")

const validationResults = {
	passed: 0,
	failed: 0,
	total: 0,
	details: [],
}

function validateFile(filePath, description) {
	console.log(`📄 Checking ${description}...`)

	if (fs.existsSync(filePath)) {
		console.log(`✅ ${description} - EXISTS`)
		validationResults.passed++
		validationResults.details.push({
			file: filePath,
			status: "EXISTS",
			description: description,
		})
	} else {
		console.log(`❌ ${description} - MISSING`)
		validationResults.failed++
		validationResults.details.push({
			file: filePath,
			status: "MISSING",
			description: description,
		})
	}

	validationResults.total++
	console.log("")
}

function validateFileContent(filePath, searchText, description) {
	console.log(`🔍 Checking ${description}...`)

	if (fs.existsSync(filePath)) {
		const content = fs.readFileSync(filePath, "utf8")
		if (content.includes(searchText)) {
			console.log(`✅ ${description} - FOUND`)
			validationResults.passed++
			validationResults.details.push({
				file: filePath,
				status: "FOUND",
				description: description,
				searchText: searchText,
			})
		} else {
			console.log(`❌ ${description} - NOT FOUND`)
			validationResults.failed++
			validationResults.details.push({
				file: filePath,
				status: "NOT FOUND",
				description: description,
				searchText: searchText,
			})
		}
	} else {
		console.log(`❌ ${description} - FILE MISSING`)
		validationResults.failed++
		validationResults.details.push({
			file: filePath,
			status: "FILE MISSING",
			description: description,
		})
	}

	validationResults.total++
	console.log("")
}

// Core Implementation Files
console.log("=== Core Implementation Files ===")
validateFile("src/core/tools/updateTodoListTool.ts", "Todo List Tool Implementation")
validateFile("src/core/environment/reminder.ts", "Reminder Formatting Module")
validateFile("src/shared/todo.ts", "Shared Todo Utilities")

// Integration Files
console.log("=== Integration Files ===")
validateFile("webview-ui/src/components/chat/IntegratedTodoList.tsx", "Integrated Todo List UI Component")

// Test Files
console.log("=== Test Files ===")
validateFile("src/test/todo-system.test.ts", "Todo System Unit Tests")
validateFile("src/test/todo-integration.test.ts", "Todo System Integration Tests")

// Documentation
console.log("=== Documentation ===")
validateFile("docs/todo-list-system.md", "Todo List System Documentation")

// System Integration Checks
console.log("=== System Integration Checks ===")

validateFileContent("src/core/prompts/model_prompts/claude4.ts", "update_todo_list", "Claude 4 Prompt - Todo Tool Definition")

validateFileContent(
	"src/core/prompts/model_prompts/claude4-experimental.ts",
	"updateTodoListToolDefinition",
	"Claude 4 Experimental Prompt - Todo Tool Definition",
)

validateFileContent("src/core/task/ToolExecutor.ts", 'case "update_todo_list"', "Tool Executor - Todo Tool Handler")

validateFileContent("src/core/task/TaskState.ts", "todoList: TodoItem[]", "Task State - Todo List Property")

validateFileContent("src/core/assistant-message/index.ts", '"update_todo_list"', "Assistant Message - Tool Name Registration")

validateFileContent("src/shared/ExtensionMessage.ts", "user_edit_todos", "Extension Message - User Edit Todos Message Type")

validateFileContent("src/shared/WebviewMessage.ts", "updateTodoList", "Webview Message - Todo Update Message Type")

validateFileContent("src/core/controller/index.ts", 'case "updateTodoList"', "Controller - Todo Update Message Handler")

validateFileContent(
	"webview-ui/src/components/chat/chat-view/components/layout/InputSection.tsx",
	"IntegratedTodoList",
	"Input Section - Integrated Todo List Component Usage",
)

// Proto Integration Checks
console.log("=== Proto Integration Checks ===")

validateFileContent("proto/ui.proto", "USER_EDIT_TODOS", "Proto Definition - User Edit Todos Message Type")

validateFileContent(
	"src/shared/proto-conversions/cline-message.ts",
	"user_edit_todos: ClineSay.USER_EDIT_TODOS",
	"Proto Conversion - User Edit Todos Mapping",
)

// Task Integration Checks
console.log("=== Task Integration Checks ===")

validateFileContent("src/core/task/index.ts", "formatReminderSection", "Task Class - Reminder Section Integration")

validateFileContent("src/core/task/index.ts", "this.taskState.todoList = []", "Task Class - Todo List Initialization")

// Print Summary
console.log("📊 Validation Summary")
console.log("=====================")
console.log(`Total checks: ${validationResults.total}`)
console.log(`Passed: ${validationResults.passed}`)
console.log(`Failed: ${validationResults.failed}`)
console.log(`Success rate: ${((validationResults.passed / validationResults.total) * 100).toFixed(1)}%`)

if (validationResults.failed > 0) {
	console.log("\n❌ Some validations failed:")
	validationResults.details
		.filter((detail) => detail.status === "MISSING" || detail.status === "NOT FOUND" || detail.status === "FILE MISSING")
		.forEach((detail) => {
			console.log(`   - ${detail.description}: ${detail.status}`)
			if (detail.searchText) {
				console.log(`     Looking for: "${detail.searchText}"`)
			}
		})
	console.log("\n🔧 Please review the implementation and ensure all components are properly integrated.")
	process.exit(1)
} else {
	console.log("\n✅ All validations passed! Todo list system implementation is complete.")

	console.log("\n🚀 Next Steps:")
	console.log("1. Test the system by starting a new task")
	console.log("2. Ask the AI assistant to create a todo list")
	console.log("3. Verify that todos appear in the UI")
	console.log("4. Test status updates and persistence")
	console.log("5. Check that todos are restored when resuming tasks")

	process.exit(0)
}
