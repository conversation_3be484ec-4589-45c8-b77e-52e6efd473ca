/**
 * Memory entry representing a classified and summarized user input
 */
export interface MemoryEntry {
	id: string
	category: string // Dynamic category based on topic/theme
	summary: string
	originalInput: string
	timestamp: number
	confidence: number // 0-1, confidence in classification
}

/**
 * Memory classification result from AI analysis
 */
export interface MemoryClassificationResult {
	category: string // Dynamic category based on topic/theme
	summary: string
	confidence: number
	reasoning?: string
}

/**
 * Configuration for memory generation
 */
export interface MemoryConfig {
	enabled: boolean
	minInputLength: number // Minimum input length to process
	maxEntriesPerCategory: number // Maximum entries to keep per category
	confidenceThreshold: number // Minimum confidence to save entry
	debounceMs: number // Debounce time for processing
	useUnifiedProcessing: boolean // Whether to use unified processing (one AI call)
}

/**
 * Memory storage structure for the markdown file - now supports dynamic categories
 */
export interface MemoryStorage {
	[category: string]: MemoryEntry[]
}

/**
 * Example topic categories for reference (not exhaustive)
 */
export const EXAMPLE_TOPIC_CATEGORIES = {
	"Code Context Extraction": "代码上下文提取相关的偏好和需求",
	"File Management": "文件管理相关的偏好和操作",
	"Memory Service": "内存服务相关的配置和优化",
	"Service Initialization": "服务初始化相关的模式和偏好",
	"User Input Handling": "用户输入处理相关的逻辑和偏好",
	"UI/UX Preferences": "界面和用户体验相关的偏好",
	"Context Menu": "上下文菜单相关的配置和偏好",
	Testing: "测试相关的偏好和方法",
	"Code Style": "代码风格和规范相关的偏好",
	"Development Tools": "开发工具相关的偏好和配置",
	"API Integration": "API集成相关的偏好和模式",
	"Error Handling": "错误处理相关的偏好和模式",
	Performance: "性能优化相关的偏好和策略",
	Security: "安全相关的偏好和实践",
	Documentation: "文档相关的偏好和标准",
}

/**
 * Memory update operation types
 */
export enum MemoryUpdateOperation {
	ADD = "add",
	UPDATE = "update",
	REMOVE = "remove",
	MERGE = "merge",
}

/**
 * Memory update instruction from AI model
 */
export interface MemoryUpdateInstruction {
	operation: MemoryUpdateOperation
	category: string // Dynamic category based on topic/theme
	targetId?: string // For update/remove operations
	newEntry?: {
		summary: string
		confidence: number
		reasoning?: string
	}
	mergeWith?: string[] // IDs to merge with
}

/**
 * Complete memory update result from AI analysis
 */
export interface MemoryUpdateResult {
	shouldUpdate: boolean
	instructions: MemoryUpdateInstruction[]
	reasoning: string
	newEntryClassification?: MemoryClassificationResult
}

/**
 * Default memory configuration
 */
export const DEFAULT_MEMORY_CONFIG: MemoryConfig = {
	enabled: true,
	minInputLength: 10,
	maxEntriesPerCategory: 20,
	confidenceThreshold: 0.6,
	debounceMs: 2000,
	useUnifiedProcessing: true, // Default to using unified processing
}
