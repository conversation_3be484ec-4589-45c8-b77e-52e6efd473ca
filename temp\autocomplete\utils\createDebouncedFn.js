"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDebouncedFn = createDebouncedFn;
const vscode = __importStar(require("vscode"));
function createDebouncedFn(fn, delay) {
    let timeoutId = null;
    let currentCancellation = null;
    return async function (...args) {
        // Cancel previous request
        if (currentCancellation) {
            currentCancellation.cancel();
        }
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
        return new Promise((resolve) => {
            timeoutId = setTimeout(async () => {
                currentCancellation = new vscode.CancellationTokenSource();
                try {
                    const result = await fn(...args);
                    // Only resolve if not cancelled
                    if (!currentCancellation.token.isCancellationRequested) {
                        resolve(result);
                    }
                    else {
                        resolve(null);
                    }
                }
                catch (error) {
                    // All errors (including cancellation) resolve to null
                    if (!(error instanceof vscode.CancellationError)) {
                        console.error("Debounced function error:", error);
                    }
                    resolve(null);
                }
            }, delay);
        });
    };
}
